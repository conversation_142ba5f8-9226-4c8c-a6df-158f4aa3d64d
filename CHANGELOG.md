# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [6.8.2] - 2025-07-29

### 🐛 Bug Fixes
- **Android 12 Compatibility:** Fixed critical crash in navigation system when using back button on Android 12 and lower
  - **Issue:** `NoSuchMethodError: removeLast()` method not available in List interface on Android API < 33
  - **Solution:** Replaced `removeLast()` with `removeAt(size - 1)` for backward compatibility
  - **Impact:** Navigation now works correctly on all Android versions from API 21+
- **Navigation System:** Removed unnecessary `@RequiresApi` annotation and Android version imports
- **Code Cleanup:** Cleaned up unused imports in NavigationViewModel

### 🔧 Technical Changes
- **NavigationViewModel:** Updated `navigateBack()` method to use API-compatible list operations
- **Compatibility:** Ensured all navigation operations work on Android 5.0 (API 21) and above

## [6.8.1] - 2025-01-27

### 🗑️ Feature Removal - RAM Testing System
- **Complete RAM Test Removal:** Permanently removed the entire RAM testing functionality from the application
- **Deleted Files:** Removed all RAM test-related files including:
  - `RamTester.kt` - Main RAM testing algorithm class
  - `RamTestScreen.kt` - RAM test UI screen
  - `RamTestViewModel.kt` - RAM test view model
  - `RamTestConfig.kt`, `RamTestResult.kt` - RAM test model files
  - `RamTestTypeCard.kt`, `RamTestMetricsDisplay.kt` - RAM test UI components
- **Navigation Cleanup:** Removed RAM test navigation entries from all navigation files
- **UI Cleanup:** Removed RAM test menu items from Tests screen and main dashboard
- **Resource Cleanup:** Removed all RAM test string resources from both English and Persian language files
- **Code Cleanup:** Cleaned up all imports, references, and dependencies related to RAM testing

## [6.8.0] - 2025-07-24

### ✨ New Feature
- **Enhanced Testing Framework:** Improved the overall testing infrastructure and UI components.
- **Bilingual Support:** Enhanced English and Persian string resources for testing interfaces.

## [6.7.4] - 2025-07-15

### 🎨 UI Consistency Enhancement
- **🎨 TopAppBar Color Consistency:** Fixed UI consistency issue where top app bar colors didn't match screen background colors throughout the application
- **🔧 Custom TopAppBar Component:** Created `KavoshTopAppBar` component that automatically uses background color for consistent theming
- **📱 Universal Application:** Applied consistent app bar styling across all screens including diagnostic tests, detail screens, settings, and about pages
- **🌈 Theme Compatibility:** Ensured compatibility with all existing themes (Light/Dark/AMOLED) and custom color themes
- **🔄 RTL/LTR Support:** Maintained proper RTL/LTR layout support in the new consistent app bar implementation

### 🔧 Technical Improvements
- **🎨 Color Scheme Integration:** TopAppBar now uses `MaterialTheme.colorScheme.background` for container color and `onBackground` for content colors
- **📦 Component Reusability:** Created reusable `KavoshTopAppBar` with both composable title and string title variants
- **🧹 Code Standardization:** Updated all screens to use the new consistent TopAppBar component instead of standard Material 3 TopAppBar
- **💬 Persian Documentation:** Added comprehensive Persian comments explaining the UI consistency improvements
- **🔧 Material Design 3 Compliance:** Maintained full Material Design 3 compliance while achieving visual consistency

## [6.7.3] - 2025-07-14

### 🎨 Memory Speed Test UI/UX Refinements
- **🎨 Color Consistency:** Fixed color mismatch between speed indicator cards and their corresponding charts - write and read charts now use matching colors
- **📈 Chart Smoothing:** Implemented speed data smoothing using moving averages to eliminate dramatic ups and downs, creating more realistic and gradual speed variations
- **🔴 Removed Data Points:** Eliminated all data point circles/dots from chart lines for cleaner, smoother line visualization
- **🚫 Removed Export Features:** Completely removed all export and sharing functionality from storage speed test section including dropdown menus, export buttons, and file download icons
- **✨ Enhanced Chart Quality:** Improved chart rendering with gradient backgrounds and smoother line transitions

### 🔧 Technical Improvements
- **📊 Chart Engine:** Added `smoothSpeedData()` function with moving average algorithm for realistic speed curve visualization
- **🎨 Color Management:** Ensured consistent color scheme between speed indicators and charts using MaterialTheme.colorScheme
- **🧹 Code Cleanup:** Removed all export-related imports, functions, and UI components from storage test components
- **📈 Data Processing:** Enhanced speed data processing with intelligent smoothing while maintaining meaningful speed variations

## [6.7.2] - 2025-07-14

### 🐛 Final Critical Memory Speed Test Fixes
- **📊 Write Chart Rendering:** Fixed write speed chart display issue - chart now properly renders colored lines and data points
- **⏱️ Test Duration:** Corrected test duration to exactly 10 seconds total (5s write + 5s read) as originally specified
- **📁 File Size Display:** Fixed test history showing correct "1 GB" file size instead of "0 GB"
- **🎯 Chart Visualization:** Enhanced chart rendering with proper data point handling, gradient fills, and empty state messages
- **📈 Data Collection:** Improved speed data collection and real-time chart updates with debug logging

### 🔧 Technical Improvements
- **📊 Chart Engine:** Enhanced `StorageSpeedChart.kt` with robust rendering for single and multiple data points
- **⏱️ Duration Logic:** Fixed timing variables in `MemoryDataSource.kt` to ensure precise 10-second test execution
- **📁 Size Calculation:** Corrected file size reporting in test results and history display components
- **🎨 Visual Enhancements:** Added gradient backgrounds, data point markers, and min/max speed indicators to charts

## [6.7.1] - 2025-07-14

### 🐛 Critical Memory Speed Test Fixes
- **📊 Separate Charts:** Fixed chart display to show independent write and read speed charts with distinct visual separation
- **📈 Progress Bar Sync:** Fixed progress bar synchronization to accurately match the numerical percentage displayed
- **⚡ Speed Accuracy:** Corrected speed calculation algorithms to restore accurate measurements (400-500 MB/s range)
- **🔧 Buffer Optimization:** Increased buffer size to 1MB and optimized file size to 200MB for better performance measurement
- **📊 Real-time Updates:** Enhanced live speed calculation with instant and average speed combination for accurate monitoring

### 🔧 Technical Improvements
- **📈 Chart Architecture:** Redesigned `StorageSpeedChart.kt` with separate chart components for write and read phases
- **📊 Progress Indicator:** Replaced WaveLoadingIndicator with precise LinearProgressIndicator for exact progress tracking
- **⚡ Speed Calculation:** Fixed speed measurement formulas with proper bytes-to-MB/s conversion and interval-based calculations
- **🎯 Performance Tuning:** Optimized buffer sizes, sampling intervals, and file handling for realistic speed measurements

## [6.7.0] - 2025-07-14

### 🚀 Enhanced Memory Speed Test (Major Feature)
- **📊 1GB Test File:** Upgraded from 100MB to 1GB test file for accurate storage speed measurement
- **🔐 Permission Dialog:** User consent dialog explaining test requirements before creating large test file
- **📈 Real-time Speed Charts:** Live monitoring with beautiful charts showing read/write speeds during test execution
- **⏱️ 10-Second Test Duration:** Optimized test duration for comprehensive speed analysis
- **📚 Test History:** Persistent storage of test results with expandable history section
- **📤 Export Functionality:** Multiple export formats (TXT, PDF, JSON, HTML, Excel, QR Code) for test results
- **🎨 Professional UI:** Enhanced interface with loading animations and sophisticated design
- **🌐 Bilingual Support:** Complete Persian/English localization with Persian comments in code

### 🔧 Technical Enhancements
- **📁 New Data Models:** `StorageSpeedTestResult`, `SpeedDataPoint`, `StorageTestSummary` for comprehensive test data
- **🎯 Enhanced MemoryDataSource:** New `performEnhancedStorageSpeedTest()` method with real-time callbacks
- **💾 History Persistence:** SharedPreferences integration for test history storage
- **📊 Live Chart Component:** `StorageSpeedChart` with real-time data visualization
- **🔒 Permission Management:** `StoragePermissionDialog` component for user consent
- **🎪 Enhanced ViewModel:** Complete rewrite of `StorageTestViewModel` with advanced state management

### 🎨 UI/UX Improvements
- **✨ Professional Loading Animations:** Using existing `ProfessionalLoadingIndicator` and `WaveLoadingIndicator`
- **📋 Expandable History Cards:** Clean, minimal interactions following Material Design 3 guidelines
- **🎯 Status Messages:** Real-time feedback during test phases (creating file, testing write/read, cleanup)
- **📊 Live Speed Indicators:** Numerical values alongside chart visualization
- **🔄 Enhanced Progress Tracking:** Detailed progress with phase-specific status messages

## [6.6.1] - 2025-07-14

### 🐛 Bug Fixes
- **🔄 Navigation Stack Fix:** Fixed back button behavior to properly return to the correct section instead of always going to main dashboard
- **📱 Section-Aware Navigation:** Back button now maintains proper navigation stack within each bottom navigation section (Information, Tests, Settings, Share)
- **🎯 Bottom Navigation Context:** Navigation system now remembers which section user was in when navigating to detail screens
- **⚡ Improved User Experience:** Users can now navigate within Tests section and return to Tests list instead of being redirected to main dashboard

### 🔧 Technical Changes
- **📊 NavigationViewModel Enhancement:** Added section-aware navigation with `currentBottomNavSection` state tracking
- **🎪 MainScreen Integration:** Updated to use NavigationViewModel for bottom navigation section management
- **📋 Back Stack Management:** Improved back stack logic to preserve section context during navigation
- **🎯 State Preservation:** Navigation state properly maintained across screen transitions

## [6.6.0] - 2025-07-14

### 🎨 Enhanced Animation System
- **⚡ Professional Page Transitions:** Smooth horizontal sliding animations between screens with RTL/LTR language direction support
- **🔄 Advanced Loading Animations:** Professional pulsing indicators and wave-style progress bars for diagnostic tests
- **✨ Enhanced Micro-interactions:** Button press animations with scale effects, card hover animations, and smooth state transitions
- **🎯 Material Design 3 Compliance:** All animations follow MD3 guidelines with consistent timing and easing curves
- **🚀 Performance Optimized:** 60fps animations with proper animation specifications and spring physics

### 🎭 Animation Components
- **📱 AnimatedScreenTransition:** New component for smooth page navigation with directional awareness
- **💫 ProfessionalLoadingIndicator:** Advanced loading animation with pulsing effects for diagnostic screens
- **🌊 WaveLoadingIndicator:** Wave-style progress animation for storage and performance tests
- **🎪 AnimatedButton & AnimatedCard:** Enhanced interactive components with press feedback
- **📋 AnimatedBottomSheet:** Smooth bottom sheet animations with spring physics

### 🔧 Technical Improvements
- **⚙️ Animation Constants:** Centralized animation timing and specifications for consistency
- **🎨 Animation Utilities:** Reusable animation components and helper functions
- **📐 RTL/LTR Support:** Proper animation direction handling for Persian and English layouts
- **🎪 Spring Physics:** Natural feeling animations using spring-based transitions
- **🚫 No Ripple Effects:** Completely removed all ripple effects and visual click indicators throughout the app
- **🎯 Clean Interactions:** All clickable elements work functionally without visual feedback animations

## [6.5.0] - 2025-07-07

### Enhanced Sensor Interface
- **📱 Modern Sensor Cards:** Redesigned sensor list with clickable cards instead of inline test buttons
- **📋 Sensor Detail BottomSheet:** Beautiful modal bottom sheet showing comprehensive sensor information
- **🔬 Smart Test Indicators:** Visual indicators showing which sensors support live testing
- **📊 Detailed Sensor Info:** Complete sensor details including vendor, type, and test capabilities

### New Export Formats
- **🌐 HTML Report:** Beautiful web-based reports viewable in any browser with professional styling and responsive design
- **📊 Excel Export:** Professional Excel files (.xlsx) with formatted tables, styling, and comprehensive data organization
- **📱 QR Code Sharing:** Generate QR codes for quick sharing of device information and diagnostic results

### Enhanced Export System
- **📤 Extended Format Support:** Added 3 new export formats to existing TXT, PDF, and JSON options
- **🎨 Professional HTML Reports:** Styled web reports with CSS, responsive design, and beautiful formatting
- **📋 Advanced Excel Reports:** Multi-sheet Excel files with proper formatting, colors, and data organization
- **🔗 QR Code Generation:** Smart QR codes with error correction and optimized data encoding
- **📱 QR Code Sharing:** Complete device information in QR codes with sharing capabilities
- **🌐 Online Sharing URLs:** Generate shareable URLs for device information via QR codes

### UI/UX Improvements
- **🎨 Redesigned Export Interface:** New unified export card with dropdown format selection instead of multiple cards
- **📱 Improved User Experience:** Single card with format selector and save button for cleaner interface
- **🔄 Streamlined Workflow:** Simplified export process with better visual hierarchy and organization

### Technical Improvements
- **📚 Apache POI Integration:** Added Excel generation capabilities with professional formatting
- **🎯 ZXing QR Library:** Integrated QR code generation with customizable styling and error handling
- **🏗️ Report Generator Classes:** New HtmlReportGenerator and ExcelReportGenerator for advanced formatting
- **🔧 Enhanced Export Pipeline:** Improved export system supporting all 6 formats across all diagnostic tools
- **📁 FileProvider Support:** Added secure file sharing capabilities for QR code sharing

### Dependencies Added
- **📊 Apache POI 5.2.4:** For Excel file generation and manipulation
- **📱 ZXing Core 3.5.2:** For QR code generation and encoding
- **🔗 ZXing Android 4.3.0:** For Android-specific QR code functionality

## [6.3.0] - 2025-07-04

### Enhanced User Experience
- **🎯 Manual Test Initiation:** All diagnostic tests now start with user button click instead of auto-starting
- **📊 Test History Management:** Complete history tracking for all diagnostic tools with expandable sections
- **🌐 Full Bilingual Support:** All diagnostic screens now fully support Persian and English languages
- **📤 Export Capabilities:** Added export functionality for all diagnostic reports (Health Check, Performance Score, Device Comparison)

### New Features
- **📋 Expandable History Sections:** Collapsible test history with date/time stamps and quick overview
- **🔄 Test Action Controls:** "New Test" and "Export Report" buttons for better user control
- **📅 Detailed Test Records:** Each test saves device info, Android version, and complete results
- **🎨 Enhanced Visual Design:** Improved cards, animations, and color-coded status indicators

### Technical Improvements
- **💾 History Storage:** Automatic saving of last 10 tests for each diagnostic tool
- **🎭 State Management:** Enhanced ViewModel with history tracking and manual test triggering
- **📱 UI Components:** New reusable components for start cards, history sections, and action buttons
- **🌍 Localization:** 50+ new string resources for complete bilingual experience

## [6.2.0] - 2025-07-04

### Added
- **🛠️ Health Check Tool:** Comprehensive device health analysis with 8 categories (Performance, Storage, Battery, Temperature, Memory, Network, Security, System)
- **📊 Performance Score:** Advanced benchmarking system with CPU, GPU, RAM, and Storage tests with letter grades (S+ to F)
- **⚖️ Device Comparison:** Compare your device with similar models, view rankings and specifications side-by-side
- **🎯 Diagnostic Data Models:** Complete data structure for health checks, performance scoring, and device comparisons
- **📈 Visual Analytics:** Beautiful charts, progress indicators, and comparison bars for diagnostic results

### Enhanced
- **🧪 Tests Screen:** Added three new diagnostic tools to the existing hardware tests
- **🌐 Multilingual Support:** Full Persian and English support for all new diagnostic features
- **🎨 Professional UI:** Modern card-based design with animated progress indicators and color-coded results

### Technical Improvements
- **🏗️ DiagnosticDataSource:** New data source with comprehensive device analysis algorithms
- **🎭 DiagnosticViewModel:** Reactive state management for all diagnostic operations
- **📱 Responsive Design:** Optimized layouts for different screen sizes and orientations

## [6.1.0] - 2025-07-04

### Changed
- **🎯 Enhanced Navigation Animations:** Improved floating bottom navigation with horizontal sliding animations
- **📱 RTL/LTR Support:** Navigation text animations now respect language direction (RTL for Persian, LTR for English)
- **🎨 Refined UI Behavior:** Icons-only display in normal state, text appears with smooth horizontal animation on selection
- **⚡ Smooth Background Transitions:** Background color changes with sliding animation instead of fade in/out

### Technical Improvements
- **🔧 Animation System:** Replaced fade animations with expandHorizontally/shrinkHorizontally for better UX
- **🌐 Layout Direction Awareness:** Added LocalLayoutDirection support for proper RTL/LTR text animations
- **🎭 Enhanced Visual Feedback:** Improved icon scaling and color transitions for better user interaction

## [6.0.0] - 2025-07-04

### Changed
- **🎨 Complete UI Redesign:** Revolutionary new interface with floating bottom navigation
- **📱 Modern Navigation:** Replaced top app bar with beautiful floating bottom navigation bar
- **🏠 Main Screen Restructure:** New tabbed interface with four main sections: Info, Tests, Settings, Share
- **⚡ Improved User Experience:** Streamlined navigation and better content organization

### Added
- **🧪 Dedicated Tests Screen:** Centralized location for all hardware testing tools
- **📤 Share Screen:** Dedicated screen for export and sharing options with multiple formats
- **🎯 Floating Bottom Navigation:** Modern, animated navigation bar with smooth transitions
- **📋 Organized Content:** Logical grouping of features into distinct sections

### Removed
- **📊 Top App Bar:** Eliminated cluttered top bar for cleaner interface
- **🔧 Menu Overflow:** Moved all actions to dedicated screens for better accessibility

### Previous Updates

## [5.8.0] - 2025-07-04

### Changed
- **Settings Screen Redesign:** Complete redesign of the settings screen with modern Material Design 3 components
- **Improved User Experience:** Added expandable sections, smooth animations, and better organization
- **Enhanced Visual Design:** New card-based layout with proper spacing and typography
- **Better Accessibility:** Improved navigation and interaction patterns

### Added
- **Sectioned Settings Layout:** Organized settings into logical groups (Appearance, Dashboard, Performance, About)
- **Animated Transitions:** Smooth expand/collapse animations for settings sections
- **Advanced Settings:** Added cache management and settings reset options
- **New String Resources:** Added comprehensive localization for new UI elements

### Technical Improvements
- **Component Architecture:** Modular composable components for better maintainability
- **State Management:** Improved state handling with proper separation of concerns
- **Code Documentation:** Added comprehensive Persian comments for all new components
- **Cache Management:** Implemented cache clearing functionality with confirmation dialogs
- **Settings Reset:** Added complete settings reset to default values
- **Confirmation Dialogs:** Added user-friendly confirmation dialogs for destructive actions

### Fixed
- **String Resource Usage:** Fixed compilation errors related to stringResource() usage in non-Composable contexts
- **Navigation Conflicts:** Resolved duplicate method definitions in NavigationViewModel
- **Export Format Support:** Added missing JSON export format support
- **Settings Integration:** Moved dashboard edit functionality to settings screen
- **Export Crash Fix:** Fixed NotImplementedError in ReportFormatter for APPS category
- **Quick Share Implementation:** Added functional quick share feature with device summary
- **Navigation Issues:** Fixed back button behavior in Settings and Tests screens
- **Storage Test Navigation:** Added navigation to storage speed test (placeholder screen)
- **Repository Methods:** Added getDeviceInfo() and getCurrentBatteryInfo() methods to DeviceInfoRepository
- **DeviceInfo Model:** Fixed installedApps field reference to apps field in DeviceInfo model
- **Complete Storage Test:** Implemented full storage speed test screen with progress tracking and results display
- **Activity Parameter Fix:** Fixed null Activity parameter issues by adding getBasicDeviceInfo() method
- **Export Functionality:** Improved export system to work with cached data or basic device info when Activity is not available

## [4.6.0] - 2025-07-03

### Added
- **Installed Applications Manager:** A new screen to list all user and system applications, showing details like version, installation date, and permissions.

---