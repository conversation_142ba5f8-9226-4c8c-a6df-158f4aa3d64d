package ir.dekot.kavosh.core.navigation

import androidx.annotation.DrawableRes
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Build
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Share
import androidx.compose.ui.graphics.vector.ImageVector
import ir.dekot.kavosh.R

/**
 * enum برای آیتم‌های Navigation Bar پایین
 * شامل چهار بخش اصلی برنامه
 */
enum class BottomNavItem(
    val route: String,
    val icon: ImageVector? = null,
    @param:DrawableRes val drawableRes: Int? = null,
    val titleResId: Int,
    val descriptionResId: Int
) {
    /**
     * بخش اطلاعات - نمایش تمام کارت‌های اطلاعات دستگاه
     */
    INFO(
        route = "info",
        drawableRes = R.drawable.ic_dashboard,
        titleResId = R.string.nav_dashboard,
        descriptionResId = R.string.nav_dashboard_desc
    ),
    
    /**
     * بخش تست‌ها - ابزارهای تست سخت‌افزار
     */
    TESTS(
        route = "tests",
        drawableRes = R.drawable.ic_test,
        titleResId = R.string.nav_tests,
        descriptionResId = R.string.nav_tests_desc
    ),
    
    /**
     * بخش تنظیمات - تنظیمات برنامه
     */
    SETTINGS(
        route = "settings",
        drawableRes = R.drawable.ic_settings,
        titleResId = R.string.nav_settings,
        descriptionResId = R.string.nav_settings_desc
    ),
    
    /**
     * بخش اشتراک‌گذاری - خروجی و اشتراک‌گذاری اطلاعات
     */
    SHARE(
        route = "share",
        drawableRes = R.drawable.ic_share,
        titleResId = R.string.nav_share,
        descriptionResId = R.string.nav_share_desc
    )
}
