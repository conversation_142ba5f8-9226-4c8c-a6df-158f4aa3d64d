package ir.dekot.kavosh.core.permission.model

import android.Manifest
import androidx.annotation.StringRes
import ir.dekot.kavosh.R

/**
 * مدل برای نمایش پرمیشن‌های مورد نیاز برنامه
 * Model for representing app permissions
 */
data class AppPermission(
    val permission: String,
    @param:StringRes val titleRes: Int,
    @param:StringRes val descriptionRes: Int,
    val isRequired: Boolean = true,
    val isGranted: Boolean = false
)

/**
 * لیست تمام پرمیشن‌های مورد نیاز برنامه
 * List of all required app permissions
 */
object AppPermissions {
    
    /**
     * دریافت لیست تمام پرمیشن‌های مورد نیاز
     * Get list of all required permissions
     */
    fun getAllPermissions(): List<AppPermission> = listOf(
        AppPermission(
            permission = Manifest.permission.READ_PHONE_STATE,
            titleRes = R.string.permission_phone_state_title,
            descriptionRes = R.string.permission_phone_state_description,
            isRequired = true
        ),
        AppPermission(
            permission = Manifest.permission.ACCESS_FINE_LOCATION,
            titleRes = R.string.permission_location_title,
            descriptionRes = R.string.permission_location_description,
            isRequired = true
        ),
        AppPermission(
            permission = Manifest.permission.CAMERA,
            titleRes = R.string.permission_camera_title,
            descriptionRes = R.string.permission_camera_description,
            isRequired = false
        ),
        AppPermission(
            permission = Manifest.permission.ACCESS_WIFI_STATE,
            titleRes = R.string.permission_wifi_state_title,
            descriptionRes = R.string.permission_wifi_state_description,
            isRequired = true
        ),
        AppPermission(
            permission = Manifest.permission.ACCESS_NETWORK_STATE,
            titleRes = R.string.permission_network_state_title,
            descriptionRes = R.string.permission_network_state_description,
            isRequired = true
        )
    )
    
    /**
     * دریافت فقط پرمیشن‌های خطرناک که نیاز به درخواست دارند
     * Get only dangerous permissions that need to be requested
     */
    fun getDangerousPermissions(): List<AppPermission> = listOf(
        AppPermission(
            permission = Manifest.permission.READ_PHONE_STATE,
            titleRes = R.string.permission_phone_state_title,
            descriptionRes = R.string.permission_phone_state_description,
            isRequired = true
        ),
        AppPermission(
            permission = Manifest.permission.ACCESS_FINE_LOCATION,
            titleRes = R.string.permission_location_title,
            descriptionRes = R.string.permission_location_description,
            isRequired = true
        ),
        AppPermission(
            permission = Manifest.permission.CAMERA,
            titleRes = R.string.permission_camera_title,
            descriptionRes = R.string.permission_camera_description,
            isRequired = false
        )
    )
}
