package ir.dekot.kavosh.core.util

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import java.util.Locale

/**
 * مدیر تغییر سریع زبان بدون recreate کامل Activity
 * Fast language change manager without full Activity recreate
 */
object LanguageManager {
    
    /**
     * CompositionLocal برای نگهداری زبان فعلی
     */
    val LocalLanguage = staticCompositionLocalOf { "fa" }
    
    /**
     * تغییر سریع زبان فقط برای UI بدون recreate کامل
     * Fast language change for UI only without full recreate
     */
    fun changeLanguageQuick(activity: Activity, languageCode: String) {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)

        // تغییر Configuration فقط برای Resources
        val configuration = Configuration(activity.resources.configuration)
        configuration.setLocale(locale)

        // اعمال Configuration جدید بدون recreate
        @Suppress("DEPRECATION")
        activity.resources.updateConfiguration(configuration, activity.resources.displayMetrics)

        // حذف recreate() - فقط Configuration تغییر می‌کند
        // No recreate() - only Configuration changes
    }
    
    /**
     * ایجاد Context با زبان مشخص
     * Create context with specific language
     */
    fun createLocalizedContext(context: Context, languageCode: String): Context {
        val locale = Locale(languageCode)
        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(locale)
        return context.createConfigurationContext(configuration)
    }
    
    /**
     * دریافت رشته محلی‌سازی شده
     * Get localized string
     */
    fun getLocalizedString(context: Context, stringResId: Int, languageCode: String): String {
        val localizedContext = createLocalizedContext(context, languageCode)
        return localizedContext.getString(stringResId)
    }
    
    /**
     * بررسی RTL بودن زبان
     * Check if language is RTL
     */
    fun isRtl(languageCode: String): Boolean {
        return when (languageCode) {
            "fa", "ar", "he", "ur" -> true
            else -> false
        }
    }
}

/**
 * Composable برای ارائه زبان محلی با Configuration صحیح
 * Composable for providing local language with correct Configuration
 */
@Composable
fun LocaleProvider(
    language: String,
    content: @Composable () -> Unit
) {
    val context = LocalContext.current
    val localizedContext = LanguageManager.createLocalizedContext(context, language)
    
    CompositionLocalProvider(
        LanguageManager.LocalLanguage provides language,
        LocalConfiguration provides localizedContext.resources.configuration
    ) {
        content()
    }
}

/**
 * Hook برای دسترسی به زبان فعلی
 * Hook for accessing current language
 */
@Composable
fun currentLanguage(): String {
    return LanguageManager.LocalLanguage.current
}

/**
 * Hook برای دسترسی به رشته محلی‌سازی شده
 * Hook for accessing localized string
 */
@Composable
fun localizedString(stringResId: Int): String {
    val context = LocalContext.current
    val language = currentLanguage()
    return LanguageManager.getLocalizedString(context, stringResId, language)
}
