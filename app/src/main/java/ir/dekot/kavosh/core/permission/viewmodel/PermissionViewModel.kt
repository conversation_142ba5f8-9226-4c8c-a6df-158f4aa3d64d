package ir.dekot.kavosh.core.permission.viewmodel

import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import ir.dekot.kavosh.core.permission.model.AppPermission
import ir.dekot.kavosh.core.permission.model.AppPermissions
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای مدیریت پرمیشن‌های برنامه
 * ViewModel for managing app permissions
 */
@HiltViewModel
class PermissionViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context
) : ViewModel() {

    // وضعیت نمایش دیالوگ پرمیشن‌ها
    // Permission dialog display state
    private val _showPermissionDialog = MutableStateFlow(false)
    val showPermissionDialog: StateFlow<Boolean> = _showPermissionDialog.asStateFlow()

    // لیست پرمیشن‌ها با وضعیت فعلی
    // List of permissions with current status
    private val _permissions = MutableStateFlow<List<AppPermission>>(emptyList())
    val permissions: StateFlow<List<AppPermission>> = _permissions.asStateFlow()

    // وضعیت درخواست پرمیشن‌ها
    // Permission request state
    private val _isRequestingPermissions = MutableStateFlow(false)
    val isRequestingPermissions: StateFlow<Boolean> = _isRequestingPermissions.asStateFlow()

    // شمارنده پرمیشن فعلی در حال درخواست
    // Current permission index being requested
    private val _currentPermissionIndex = MutableStateFlow(0)
    val currentPermissionIndex: StateFlow<Int> = _currentPermissionIndex.asStateFlow()

    // callback برای درخواست پرمیشن
    // Callback for permission request
    private var permissionRequestCallback: ((String) -> Unit)? = null

    init {
        loadPermissions()
    }

    /**
     * بارگذاری و بررسی وضعیت پرمیشن‌ها
     * Load and check permission status
     */
    private fun loadPermissions() {
        viewModelScope.launch {
            val dangerousPermissions = AppPermissions.getDangerousPermissions()
            val updatedPermissions = dangerousPermissions.map { permission ->
                permission.copy(
                    isGranted = ContextCompat.checkSelfPermission(
                        context,
                        permission.permission
                    ) == PackageManager.PERMISSION_GRANTED
                )
            }
            _permissions.value = updatedPermissions
        }
    }

    /**
     * بررسی اینکه آیا نیاز به نمایش دیالوگ پرمیشن است
     * Check if permission dialog should be shown
     */
    fun checkAndShowPermissionDialog(): Boolean {
        val hasUnGrantedPermissions = _permissions.value.any { !it.isGranted }
        if (hasUnGrantedPermissions) {
            _showPermissionDialog.value = true
            return true
        }
        return false
    }

    /**
     * بستن دیالوگ پرمیشن
     * Close permission dialog
     */
    fun dismissPermissionDialog() {
        _showPermissionDialog.value = false
    }

    /**
     * شروع فرآیند درخواست پرمیشن‌ها
     * Start permission request process
     */
    fun startPermissionRequest(onRequestPermission: (String) -> Unit) {
        permissionRequestCallback = onRequestPermission
        _isRequestingPermissions.value = true
        _currentPermissionIndex.value = 0
        requestNextPermission()
    }

    /**
     * درخواست پرمیشن بعدی
     * Request next permission
     */
    private fun requestNextPermission() {
        val unGrantedPermissions = _permissions.value.filter { !it.isGranted }
        val currentIndex = _currentPermissionIndex.value

        if (currentIndex < unGrantedPermissions.size) {
            val permission = unGrantedPermissions[currentIndex]
            permissionRequestCallback?.invoke(permission.permission)
        } else {
            // تمام پرمیشن‌ها درخواست شدند
            // All permissions have been requested
            _isRequestingPermissions.value = false
            _currentPermissionIndex.value = 0
            loadPermissions() // بروزرسانی وضعیت پرمیشن‌ها
        }
    }

    /**
     * پردازش نتیجه درخواست پرمیشن
     * Process permission request result
     */
    fun onPermissionResult(permission: String, isGranted: Boolean) {
        // بروزرسانی وضعیت پرمیشن در لیست
        // Update permission status in list
        val updatedPermissions = _permissions.value.map { perm ->
            if (perm.permission == permission) {
                perm.copy(isGranted = isGranted)
            } else {
                perm
            }
        }
        _permissions.value = updatedPermissions

        // درخواست پرمیشن بعدی
        // Request next permission
        if (_isRequestingPermissions.value) {
            _currentPermissionIndex.value = _currentPermissionIndex.value + 1
            requestNextPermission()
        }
    }

    /**
     * رد کردن درخواست پرمیشن‌ها
     * Skip permission request
     */
    fun skipPermissions() {
        _showPermissionDialog.value = false
        _isRequestingPermissions.value = false
        _currentPermissionIndex.value = 0
    }

    /**
     * بررسی اینکه آیا تمام پرمیشن‌های ضروری اعطا شده‌اند
     * Check if all required permissions are granted
     */
    fun areAllRequiredPermissionsGranted(): Boolean {
        return _permissions.value
            .filter { it.isRequired }
            .all { it.isGranted }
    }

    /**
     * دریافت تعداد پرمیشن‌های اعطا شده
     * Get count of granted permissions
     */
    fun getGrantedPermissionsCount(): Int {
        return _permissions.value.count { it.isGranted }
    }

    /**
     * دریافت تعداد کل پرمیشن‌ها
     * Get total permissions count
     */
    fun getTotalPermissionsCount(): Int {
        return _permissions.value.size
    }
}
