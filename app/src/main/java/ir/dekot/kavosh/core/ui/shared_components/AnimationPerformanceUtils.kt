package ir.dekot.kavosh.core.ui.shared_components

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import kotlin.system.measureTimeMillis

/**
 * بهینه‌سازی ۷: ابزارهای نظارت بر عملکرد انیمیشن
 * Optimization 7: Animation performance monitoring utilities
 */

/**
 * کلاس نظارت بر عملکرد انیمیشن
 * Animation performance monitor class
 */
data class AnimationPerformanceMetrics(
    val frameTime: Long = 0L,
    val targetFrameRate: Float = 60f,
    val actualFrameRate: Float = 0f,
    val isPerformant: Boolean = false,
    val droppedFrames: Int = 0
) {
    val performanceScore: Float
        get() = (actualFrameRate / targetFrameRate).coerceIn(0f, 1f)
}

/**
 * مانیتور عملکرد انیمیشن
 * Animation performance monitor
 */
class AnimationPerformanceMonitor {
    private var frameCount = 0
    private var startTime = 0L
    private var lastFrameTime = 0L
    private val targetFrameTime = 1000L / 60L // 16.67ms for 60fps

    fun startMonitoring() {
        frameCount = 0
        startTime = System.currentTimeMillis()
        lastFrameTime = startTime
    }

    fun recordFrame(): AnimationPerformanceMetrics {
        frameCount++
        val currentTime = System.currentTimeMillis()
        val frameTime = currentTime - lastFrameTime
        lastFrameTime = currentTime

        val totalTime = currentTime - startTime
        val actualFrameRate = if (totalTime > 0) {
            (frameCount * 1000f) / totalTime
        } else 60f

        val droppedFrames = if (frameTime > targetFrameTime) {
            ((frameTime - targetFrameTime) / targetFrameTime).toInt()
        } else 0

        return AnimationPerformanceMetrics(
            frameTime = frameTime,
            actualFrameRate = actualFrameRate,
            isPerformant = frameTime <= targetFrameTime * 1.2f, // 20% tolerance
            droppedFrames = droppedFrames
        )
    }
}

/**
 * Modifier برای نظارت بر عملکرد انیمیشن
 * Modifier for animation performance monitoring
 */
//fun Modifier.performanceMonitored(
//    onPerformanceUpdate: (AnimationPerformanceMetrics) -> Unit = {}
//): Modifier = this.graphicsLayer {
//    // نظارت بر عملکرد در لایه گرافیکی
//    // Performance monitoring in graphics layer
//    val monitor = remember { AnimationPerformanceMonitor() }
//
//    LaunchedEffect(Unit) {
//        monitor.startMonitoring()
//    }
//
//    val metrics = monitor.recordFrame()
//    onPerformanceUpdate(metrics)
//}

/**
 * انیمیشن بهینه شده برای عملکرد
 * Performance-optimized animation
 */
@Composable
fun rememberPerformanceOptimizedAnimation(
    targetValue: Float,
    animationSpec: AnimationSpec<Float> = tween(
        durationMillis = AnimationConstants.NORMAL_ANIMATION_DURATION,
        easing = LinearEasing
    ),
    label: String = "performanceAnimation"
): Animatable<Float, *> {
    val animatable = remember { Animatable(targetValue) }
    
    LaunchedEffect(targetValue) {
        withContext(Dispatchers.Main.immediate) {
            animatable.animateTo(
                targetValue = targetValue,
                animationSpec = animationSpec
            )
        }
    }
    
    return animatable
}

/**
 * تبدیل Dp به Pixel برای محاسبات بهینه
 * Convert Dp to Pixel for optimized calculations
 */
@Composable
fun Dp.toPx(): Float {
    val density = LocalDensity.current
    return with(density) { <EMAIL>() }
}

/**
 * محاسبه فاصله بهینه برای انیمیشن‌های لغزشی
 * Calculate optimized distance for slide animations
 */
fun calculateOptimizedSlideDistance(screenWidth: Float, isRtl: Boolean): Float {
    return if (isRtl) -screenWidth else screenWidth
}

/**
 * تنظیمات بهینه‌سازی انیمیشن برای دستگاه‌های مختلف
 * Animation optimization settings for different devices
 */
object DeviceOptimizedAnimations {
    
    /**
     * تشخیص سطح عملکرد دستگاه
     * Detect device performance level
     */
    fun getDevicePerformanceLevel(): PerformanceLevel {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val processors = runtime.availableProcessors()
        
        return when {
            maxMemory > 512 * 1024 * 1024 && processors >= 8 -> PerformanceLevel.HIGH
            maxMemory > 256 * 1024 * 1024 && processors >= 4 -> PerformanceLevel.MEDIUM
            else -> PerformanceLevel.LOW
        }
    }
    
    /**
     * دریافت تنظیمات انیمیشن بر اساس عملکرد دستگاه
     * Get animation settings based on device performance
     */
    fun getOptimizedAnimationSettings(level: PerformanceLevel): AnimationSettings {
        return when (level) {
            PerformanceLevel.HIGH -> AnimationSettings(
                enableComplexAnimations = true,
                maxConcurrentAnimations = 10,
                useHardwareAcceleration = true,
                animationDurationMultiplier = 1.0f
            )
            PerformanceLevel.MEDIUM -> AnimationSettings(
                enableComplexAnimations = true,
                maxConcurrentAnimations = 6,
                useHardwareAcceleration = true,
                animationDurationMultiplier = 1.1f
            )
            PerformanceLevel.LOW -> AnimationSettings(
                enableComplexAnimations = false,
                maxConcurrentAnimations = 3,
                useHardwareAcceleration = false,
                animationDurationMultiplier = 1.3f
            )
        }
    }
}

/**
 * سطح عملکرد دستگاه
 * Device performance level
 */
enum class PerformanceLevel {
    HIGH, MEDIUM, LOW
}

/**
 * تنظیمات انیمیشن
 * Animation settings
 */
data class AnimationSettings(
    val enableComplexAnimations: Boolean,
    val maxConcurrentAnimations: Int,
    val useHardwareAcceleration: Boolean,
    val animationDurationMultiplier: Float
)

/**
 * مدیر بهینه‌سازی انیمیشن
 * Animation optimization manager
 */
object AnimationOptimizationManager {
    private var currentSettings: AnimationSettings? = null
    
    fun initialize() {
        val performanceLevel = DeviceOptimizedAnimations.getDevicePerformanceLevel()
        currentSettings = DeviceOptimizedAnimations.getOptimizedAnimationSettings(performanceLevel)
    }
    
    fun getSettings(): AnimationSettings {
        return currentSettings ?: run {
            initialize()
            currentSettings!!
        }
    }
    
    fun shouldUseHardwareAcceleration(): Boolean {
        return getSettings().useHardwareAcceleration
    }
    
    fun getOptimizedDuration(baseDuration: Int): Int {
        val multiplier = getSettings().animationDurationMultiplier
        return (baseDuration * multiplier).toInt()
    }
}

/**
 * Modifier برای فعال‌سازی شرطی سخت‌افزار
 * Modifier for conditional hardware acceleration
 */
fun Modifier.conditionalHardwareAcceleration(
    condition: Boolean = AnimationOptimizationManager.shouldUseHardwareAcceleration()
): Modifier = if (condition) {
    this.graphicsLayer {
        // فعال‌سازی سخت‌افزار فقط در صورت نیاز
        // Enable hardware acceleration only when needed
        alpha = 0.99f // Trick to enable hardware layer
    }
} else {
    this
}
