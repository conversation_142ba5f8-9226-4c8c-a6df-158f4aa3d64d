package ir.dekot.kavosh.core.ui.shared_components // <-- تغییر در این خط

import androidx.compose.animation.core.EaseInOut
import androidx.compose.animation.core.EaseInOutCubic
import androidx.compose.animation.core.EaseOutBack
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * این فایل شامل کامپوزبل‌های اشتراکی و ثوابت انیمیشن است که در صفحات مختلف استفاده می‌شوند.
 */

/**
 * بهینه‌سازی ۷: ثوابت انیمیشن بهینه شده برای 60fps
 * Optimization 7: Animation constants optimized for 60fps performance
 */
@Stable
object AnimationConstants {
    // مدت زمان انیمیشن‌ها - بهینه شده برای 60fps
    // Animation durations - optimized for 60fps
    const val FAST_ANIMATION_DURATION = 150
    const val NORMAL_ANIMATION_DURATION = 300
    const val SLOW_ANIMATION_DURATION = 500
    const val EXTRA_SLOW_ANIMATION_DURATION = 800

    // انیمیشن‌های صفحه - بهینه شده برای انتقال نرم
    // Page animations - optimized for smooth transitions
    const val PAGE_TRANSITION_DURATION = 350 // کاهش یافته برای عملکرد بهتر
    const val PAGE_TRANSITION_DELAY = 50

    // انیمیشن‌های دکمه - بهینه شده برای واکنش سریع
    // Button animations - optimized for quick response
    const val BUTTON_PRESS_DURATION = 100
    const val BUTTON_RELEASE_DURATION = 200
    const val BUTTON_SCALE_PRESSED = 0.95f
    const val BUTTON_SCALE_NORMAL = 1.0f

    // انیمیشن‌های کارت - بهینه شده برای تعامل نرم
    // Card animations - optimized for smooth interaction
    const val CARD_HOVER_DURATION = 200
    const val CARD_ELEVATION_NORMAL = 4f
    const val CARD_ELEVATION_PRESSED = 8f

    // انیمیشن‌های بارگذاری - بهینه شده برای عملکرد
    // Loading animations - optimized for performance
    const val LOADING_PULSE_DURATION = 1000 // کاهش یافته برای عملکرد بهتر
    const val LOADING_WAVE_DURATION = 1800 // کاهش یافته برای عملکرد بهتر
    const val SKELETON_SHIMMER_DURATION = 1200 // کاهش یافته برای عملکرد بهتر

    // انیمیشن‌های Bottom Sheet - بهینه شده برای 60fps
    // Bottom Sheet animations - optimized for 60fps
    const val BOTTOM_SHEET_DURATION = 300 // کاهش یافته برای عملکرد بهتر
    const val BOTTOM_SHEET_SPRING_DAMPING = 0.85f // بهینه شده برای نرمی
    const val BOTTOM_SHEET_SPRING_STIFFNESS = 450f // افزایش یافته برای واکنش سریع‌تر

    // ثوابت جدید برای بهینه‌سازی عملکرد
    // New constants for performance optimization
    const val MICRO_ANIMATION_DURATION = 100 // برای انیمیشن‌های کوچک
    const val FRAME_RATE_TARGET = 60f // هدف نرخ فریم
    const val HARDWARE_LAYER_THRESHOLD = 0.5f // آستانه فعال‌سازی لایه سخت‌افزاری
}

/**
 * بهینه‌سازی ۷: مشخصات انیمیشن بهینه شده برای سخت‌افزار
 * Optimization 7: Hardware-optimized animation specifications
 */
object AnimationSpecs {
    // انیمیشن‌های عمومی - بهینه شده برای سخت‌افزار
    // General animations - hardware optimized
    val fastTween = tween<Float>(AnimationConstants.FAST_ANIMATION_DURATION, easing = FastOutSlowInEasing)
    val normalTween = tween<Float>(AnimationConstants.NORMAL_ANIMATION_DURATION, easing = EaseInOutCubic)
    val slowTween = tween<Float>(AnimationConstants.SLOW_ANIMATION_DURATION, easing = EaseInOut)

    // انیمیشن‌های دکمه - بهینه شده برای واکنش سریع
    // Button animations - optimized for quick response
    val buttonPressSpec = tween<Float>(AnimationConstants.BUTTON_PRESS_DURATION, easing = EaseInOutCubic)
    val buttonReleaseSpec = tween<Float>(AnimationConstants.BUTTON_RELEASE_DURATION, easing = EaseOutBack)

    // انیمیشن‌های صفحه - بهینه شده برای انتقال نرم
    // Page animations - optimized for smooth transitions
    val pageTransitionSpec = tween<Float>(AnimationConstants.PAGE_TRANSITION_DURATION, easing = EaseInOutCubic)

    // انیمیشن‌های فنری - بهینه شده برای عملکرد
    // Spring animations - performance optimized
    val springSpec = spring<Float>(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessMedium
    )

    val bottomSheetSpring = spring<Float>(
        dampingRatio = AnimationConstants.BOTTOM_SHEET_SPRING_DAMPING,
        stiffness = AnimationConstants.BOTTOM_SHEET_SPRING_STIFFNESS
    )

    // مشخصات جدید برای بهینه‌سازی سخت‌افزاری
    // New specs for hardware optimization
    val microTween = tween<Float>(AnimationConstants.MICRO_ANIMATION_DURATION, easing = FastOutSlowInEasing)

    val hardwareOptimizedSpring = spring<Float>(
        dampingRatio = 0.8f,
        stiffness = 500f // بهینه شده برای سخت‌افزار
    )

    val performanceSpring = spring<Float>(
        dampingRatio = 0.9f,
        stiffness = 600f // عملکرد بالا برای انیمیشن‌های سریع
    )
}

/**
 * یک تابع کمکی برای نمایش عنوان‌های داخل کارت‌های اطلاعاتی.
 */
@Composable
fun SectionTitleInCard(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(top = 16.dp, bottom = 4.dp)
    )
}

/**
 * یک کامپوننت برای نمایش یک پیام متنی در مرکز صفحه، مناسب برای وضعیت‌های خالی.
 */
@Composable
fun EmptyStateMessage(message: String) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 32.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * بهینه‌سازی ۷: دکمه با انیمیشن سخت‌افزاری بهینه شده
 * Optimization 7: Hardware-accelerated animated button
 */
@Composable
fun AnimatedButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    // بهینه‌سازی ۷: remember برای جلوگیری از ایجاد مجدد interactionSource
    // Optimization 7: remember to prevent recreating interactionSource
    val interactionSource = remember { MutableInteractionSource() }

    // بهینه‌سازی ۷: derivedStateOf برای محاسبه targetValue
    // Optimization 7: derivedStateOf for targetValue calculation
    val targetScale by remember {
        derivedStateOf {
            if (enabled) AnimationConstants.BUTTON_SCALE_NORMAL else AnimationConstants.BUTTON_SCALE_PRESSED
        }
    }

    val scale by animateFloatAsState(
        targetValue = targetScale,
        animationSpec = AnimationSpecs.buttonReleaseSpec,
        label = "buttonScale"
    )

    val alpha by animateFloatAsState(
        targetValue = if (enabled) 1f else 0.7f,
        animationSpec = AnimationSpecs.fastTween,
        label = "buttonAlpha"
    )

    Box(
        modifier = modifier
            .graphicsLayer {
                // بهینه‌سازی ۷: استفاده از graphicsLayer برای سخت‌افزار
                // Optimization 7: Use graphicsLayer for hardware acceleration
                scaleX = scale
                scaleY = scale
                this.alpha = alpha
            }
            .clickable(
                interactionSource = interactionSource,
                indication = null, // حذف کامل افکت‌های بصری
                enabled = enabled
            ) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        content()
    }
}

/**
 * بهینه‌سازی ۷: کارت با انیمیشن سخت‌افزاری بهینه شده
 * Optimization 7: Hardware-accelerated animated card
 */
@Composable
fun AnimatedCard(
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }

    val scale by animateFloatAsState(
        targetValue = if (enabled) 1.0f else 0.98f,
        animationSpec = AnimationSpecs.performanceSpring,
        label = "cardScale"
    )

    val alpha by animateFloatAsState(
        targetValue = if (enabled) 1.0f else 0.7f,
        animationSpec = AnimationSpecs.fastTween,
        label = "cardAlpha"
    )

    val elevation by animateFloatAsState(
        targetValue = if (enabled) AnimationConstants.CARD_ELEVATION_NORMAL else 0f,
        animationSpec = AnimationSpecs.normalTween,
        label = "cardElevation"
    )

    Box(
        modifier = modifier
            .graphicsLayer {
                // بهینه‌سازی ۷: استفاده از graphicsLayer برای سخت‌افزار
                // Optimization 7: Use graphicsLayer for hardware acceleration
                scaleX = scale
                scaleY = scale
                this.alpha = alpha
                shadowElevation = elevation
            }
            .then(
                if (onClick != null) {
                    Modifier.clickable(
                        interactionSource = interactionSource,
                        indication = null, // حذف کامل افکت‌های بصری
                        enabled = enabled
                    ) { onClick() }
                } else Modifier
            )
    ) {
        content()
    }
}

/**
 * بهینه‌سازی ۷: انیمیشن بارگذاری با سخت‌افزار بهینه شده
 * Optimization 7: Hardware-optimized loading animation
 */
@Composable
fun ProfessionalLoadingIndicator(
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    color: Color = MaterialTheme.colorScheme.primary
) {
    val infiniteTransition = rememberInfiniteTransition(label = "loadingTransition")

    val scale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(AnimationConstants.LOADING_PULSE_DURATION, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseScale"
    )

    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(AnimationConstants.LOADING_PULSE_DURATION, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseAlpha"
    )

    Box(
        modifier = modifier.size(size),
        contentAlignment = Alignment.Center
    ) {
        // دایره پس‌زمینه - بهینه شده با graphicsLayer
        // Background circle - optimized with graphicsLayer
        Box(
            modifier = Modifier
                .size(size)
                .graphicsLayer {
                    scaleX = scale
                    scaleY = scale
                    this.alpha = alpha * 0.3f
                }
                .clip(CircleShape)
                .background(color)
        )

        // دایره اصلی - بهینه شده با graphicsLayer
        // Main circle - optimized with graphicsLayer
        Box(
            modifier = Modifier
                .size(size * 0.6f)
                .graphicsLayer {
                    scaleX = scale * 0.8f
                    scaleY = scale * 0.8f
                    this.alpha = alpha
                }
                .clip(CircleShape)
                .background(color)
        )
    }
}

/**
 * انیمیشن بارگذاری موجی
 * مناسب برای نمایش پیشرفت تست‌ها
 */
@Composable
fun WaveLoadingIndicator(
    modifier: Modifier = Modifier,
    progress: Float = 0f,
    color: Color = MaterialTheme.colorScheme.primary
) {
    val infiniteTransition = rememberInfiniteTransition(label = "waveTransition")

    val waveOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(AnimationConstants.LOADING_WAVE_DURATION, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "waveOffset"
    )

    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = AnimationSpecs.slowTween,
        label = "waveProgress"
    )

    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        // نوار پیشرفت پس‌زمینه
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
                .padding(vertical = 4.dp)
        )

        // نوار پیشرفت انیمیت شده
        Box(
            modifier = Modifier
                .fillMaxWidth(animatedProgress)
                .padding(horizontal = 16.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(color)
                .padding(vertical = 4.dp)
                .graphicsLayer {
                    alpha = 0.8f + (waveOffset * 0.2f)
                }
        )
    }
}