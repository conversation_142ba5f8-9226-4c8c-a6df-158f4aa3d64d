package ir.dekot.kavosh.core.ui.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner

/**
 * ارائه‌دهنده محتوای پایدار برای جلوگیری از ریکامپوز در طول انیمیشن‌ها
 * این کامپوننت محتوا را در طول انیمیشن‌های تم ثابت نگه می‌دارد
 */

/**
 * CompositionLocal برای مدیریت حالت انیمیشن در سراسر برنامه
 */
val LocalAnimationState = staticCompositionLocalOf<AnimationStateManager?> { null }

/**
 * مدیریت حالت انیمیشن برای جلوگیری از ریکامپوز غیرضروری
 */
class AnimationStateManager {
    private var _isAnimating by mutableStateOf(false)
    private var _frozenContent: (@Composable () -> Unit)? by mutableStateOf(null)
    
    val isAnimating: Boolean get() = _isAnimating
    val frozenContent: (@Composable () -> Unit)? get() = _frozenContent
    
    /**
     * شروع انیمیشن و ثابت کردن محتوا
     */
    fun startAnimation(content: @Composable () -> Unit) {
        _frozenContent = content
        _isAnimating = true
    }
    
    /**
     * پایان انیمیشن و آزاد کردن محتوا
     */
    fun endAnimation() {
        _isAnimating = false
        _frozenContent = null
    }
}

/**
 * کامپوننت ارائه‌دهنده حالت انیمیشن
 */
@Composable
fun AnimationStateProvider(
    content: @Composable () -> Unit
) {
    val animationStateManager = remember { AnimationStateManager() }
    
    androidx.compose.runtime.CompositionLocalProvider(
        LocalAnimationState provides animationStateManager
    ) {
        content()
    }
}

/**
 * کامپوننت محتوای پایدار که در طول انیمیشن ریکامپوز نمی‌شود
 */
@Composable
fun StableContent(
    isAnimating: Boolean,
    content: @Composable () -> Unit
) {
    val animationStateManager = LocalAnimationState.current
    
    // اگر انیمیشن شروع شده، محتوا را ثابت کن
    LaunchedEffect(isAnimating) {
        if (isAnimating && animationStateManager != null) {
            animationStateManager.startAnimation(content)
        } else if (!isAnimating && animationStateManager != null) {
            animationStateManager.endAnimation()
        }
    }
    
    // نمایش محتوای مناسب
    if (isAnimating && animationStateManager?.frozenContent != null) {
        // در طول انیمیشن از محتوای ثابت استفاده کن
        animationStateManager.frozenContent?.invoke()
    } else {
        // در حالت عادی محتوای جاری را نمایش بده
        content()
    }
}

/**
 * Hook برای مدیریت محتوای پایدار در کامپوننت‌ها
 */
@Composable
fun rememberStableContent(
    key: Any? = null,
    content: @Composable () -> Unit
): @Composable () -> Unit {
    return remember(key) { content }
}

/**
 * کامپوننت محافظ برای جلوگیری از ریکامپوز در طول انیمیشن‌های خاص
 */
@Composable
fun AnimationProtectedContent(
    protectionKey: String,
    isProtected: Boolean = false,
    content: @Composable () -> Unit
) {
    val stableContent = remember(protectionKey, isProtected) {
        if (isProtected) content else null
    }
    
    if (isProtected && stableContent != null) {
        stableContent()
    } else {
        content()
    }
}

/**
 * کامپوننت مخصوص دیالوگ‌ها برای جلوگیری از بسته شدن ناگهانی
 */
@Composable
fun DialogStabilizer(
    isDialogOpen: Boolean,
    isAnimating: Boolean,
    onDismissRequest: () -> Unit,
    content: @Composable () -> Unit
) {
    var shouldShowDialog by remember { mutableStateOf(isDialogOpen) }
    
    // مدیریت نمایش دیالوگ در طول انیمیشن
    LaunchedEffect(isDialogOpen, isAnimating) {
        when {
            isDialogOpen -> shouldShowDialog = true
            isAnimating -> {
                // در طول انیمیشن دیالوگ را باز نگه دار
                shouldShowDialog = true
            }
            else -> {
                // پس از پایان انیمیشن دیالوگ را ببند
                shouldShowDialog = false
            }
        }
    }
    
    if (shouldShowDialog) {
        content()
    }
}

/**
 * کامپوننت مدیریت چرخه حیات برای انیمیشن‌ها
 */
@Composable
fun AnimationLifecycleManager(
    onPause: () -> Unit = {},
    onResume: () -> Unit = {},
    content: @Composable () -> Unit
) {
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> onPause()
                Lifecycle.Event.ON_RESUME -> onResume()
                else -> {}
            }
        }
        
        lifecycleOwner.lifecycle.addObserver(observer)
        
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    
    content()
}
