package ir.dekot.kavosh.core.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import ir.dekot.kavosh.core.ui.shared_components.ThemeTransitionCircularReveal
import ir.dekot.kavosh.feature_customeTheme.ColorTheme
import ir.dekot.kavosh.feature_customeTheme.Theme
import ir.dekot.kavosh.feature_customeTheme.theme.KavoshTheme
import ir.dekot.kavosh.feature_settings.viewModel.SettingsViewModel

/**
 * مدیریت انیمیشن تغییر تم
 * جدا از MainActivity برای تمیزی کد
 * با جلوگیری از ریکامپوز غیرضروری در طول انیمیشن
 */
@Composable
fun ThemeTransitionOverlay(
    settingsViewModel: SettingsViewModel,
    currentColorTheme: ColorTheme?,
    content: @Composable () -> Unit
) {
    val themeTransitionState by settingsViewModel.themeTransitionState.collectAsState()

    // محاسبه نقطه شروع انیمیشن (وسط بالای صفحه)
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    val density = LocalDensity.current
    val screenWidthPx = with(density) { screenWidth.toPx() }
    val topCenterPoint = Offset(x = screenWidthPx / 2f, y = 0f)
    //    val configuration = LocalConfiguration.current
//    val density = LocalDensity.current
//
//    val screenWidthDp = configuration.screenWidthDp.dp
//    val screenHeightDp = configuration.screenHeightDp.dp // Get screen height in Dp
//
//    val screenWidthPx = with(density) { screenWidthDp.toPx() }
//    val screenHeightPx = with(density) { screenHeightDp.toPx() } // Convert screen height to Px
//
//    val exactCenterPoint = Offset(x = screenWidthPx / 2f, y = screenHeightPx / 2f) // Calculate exact center

    Box(modifier = Modifier.fillMaxSize()) {
        // محتوای اصلی با محافظت از ریکامپوز در طول انیمیشن
        AnimationProtectedContent(
            protectionKey = "theme_transition_content",
            isProtected = themeTransitionState?.isAnimating == true
        ) {
            content()
        }

        // انیمیشن دایره‌ای تغییر تم
        themeTransitionState?.let { transitionState ->
            // محاسبه تم جدید برای نمایش در پنجره دایره‌ای
            val newUseDarkTheme = when (transitionState.newTheme) {
                Theme.SYSTEM -> isSystemInDarkTheme()
                Theme.LIGHT -> false
                Theme.DARK -> true
            }

            // تم جدید برای نمایش در پنجره دایره‌ای
            KavoshTheme(
                darkTheme = newUseDarkTheme,
                theme = transitionState.newTheme,
                colorTheme = currentColorTheme
            ) {
                ThemeTransitionCircularReveal(
                    isVisible = transitionState.isAnimating,
                    startPoint = topCenterPoint,
                    revealColor = Color.Transparent,
                    animationDurationMs = 1500,
                    onAnimationStart = {
                        // شروع انیمیشن
                    },
                    onThemeChange = {
                        // هیچ کاری نمی‌کنیم - تم در انتها تغییر می‌کند
                    },
                    onAnimationEnd = {
                        // تغییر تم در انتها و پایان انیمیشن
                        settingsViewModel.applyThemeAfterDelay()
                        settingsViewModel.finishThemeTransition()
                    }
                ) {
                    // محتوای کامل صفحه با تم جدید - فقط داخل دایره نمایش داده می‌شود
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        // استفاده از محتوای محافظت شده برای جلوگیری از ریکامپوز
                        AnimationProtectedContent(
                            protectionKey = "theme_animation_overlay",
                            isProtected = true
                        ) {
                            content()
                        }
                    }
                }
            }
        }
    }
}

/**
 * محاسبه رنگ انیمیشن بر اساس تم جدید
 */
@Composable
fun calculateRevealColor(theme: Theme): Color {
    return when (theme) {
        Theme.LIGHT -> Color.White
        Theme.DARK -> Color(0xFF121212)
        Theme.SYSTEM -> if (isSystemInDarkTheme()) Color(0xFF121212) else Color.White
    }
}
