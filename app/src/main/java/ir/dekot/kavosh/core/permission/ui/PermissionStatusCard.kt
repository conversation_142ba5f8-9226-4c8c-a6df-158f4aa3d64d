package ir.dekot.kavosh.core.permission.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import ir.dekot.kavosh.R
import ir.dekot.kavosh.core.permission.model.AppPermissions
import ir.dekot.kavosh.core.util.PermissionUtils

/**
 * کارت نمایش وضعیت پرمیشن‌ها در صفحه تنظیمات
 * Permission status card for settings screen
 */
@Composable
fun PermissionStatusCard(
    onRequestPermissions: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val permissions = AppPermissions.getDangerousPermissions()
    
    // بررسی وضعیت پرمیشن‌ها
    // Check permission status
    val permissionsWithStatus = permissions.map { permission ->
        permission.copy(
            isGranted = PermissionUtils.isPermissionGranted(context, permission.permission)
        )
    }
    
    val grantedCount = permissionsWithStatus.count { it.isGranted }
    val totalCount = permissionsWithStatus.size
    val allGranted = grantedCount == totalCount

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // هدر کارت
            // Card header
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (allGranted) Icons.Default.Security else Icons.Default.Warning,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = if (allGranted) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.error
                    }
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(R.string.app_permissions_dialog_title),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    Text(
                        text = "$grantedCount از $totalCount مجوز اعطا شده",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // نوار پیشرفت
            // Progress bar
            LinearProgressIndicator(
                progress = { grantedCount.toFloat() / totalCount.toFloat() },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp),
                color = if (allGranted) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.error
                },
                trackColor = MaterialTheme.colorScheme.surfaceVariant,
                strokeCap = androidx.compose.ui.graphics.StrokeCap.Round
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // لیست پرمیشن‌ها
            // Permissions list
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                permissionsWithStatus.forEach { permission ->
                    PermissionStatusItem(
                        permission = permission,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            // دکمه درخواست پرمیشن‌ها (فقط اگر همه اعطا نشده باشند)
            // Request permissions button (only if not all granted)
            if (!allGranted) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = onRequestPermissions,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Security,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "درخواست مجوزها",
                        style = MaterialTheme.typography.labelLarge,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * آیتم نمایش وضعیت یک پرمیشن
 * Permission status item
 */
@Composable
private fun PermissionStatusItem(
    permission: ir.dekot.kavosh.core.permission.model.AppPermission,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // آیکون پرمیشن
        // Permission icon
        Icon(
            imageVector = getPermissionIcon(permission.permission),
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // نام پرمیشن
        // Permission name
        Text(
            text = stringResource(permission.titleRes),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.weight(1f)
        )
        
        // وضعیت پرمیشن
        // Permission status
        Icon(
            imageVector = if (permission.isGranted) {
                Icons.Default.CheckCircle
            } else {
                Icons.Default.Cancel
            },
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = if (permission.isGranted) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.error
            }
        )
    }
}

/**
 * دریافت آیکون مناسب برای هر پرمیشن
 * Get appropriate icon for each permission
 */
private fun getPermissionIcon(permission: String): ImageVector {
    return when (permission) {
        android.Manifest.permission.READ_PHONE_STATE -> Icons.Default.Phone
        android.Manifest.permission.ACCESS_FINE_LOCATION,
        android.Manifest.permission.ACCESS_COARSE_LOCATION -> Icons.Default.LocationOn
        android.Manifest.permission.CAMERA -> Icons.Default.CameraAlt
        android.Manifest.permission.ACCESS_WIFI_STATE,
        android.Manifest.permission.CHANGE_WIFI_STATE -> Icons.Default.Wifi
        android.Manifest.permission.ACCESS_NETWORK_STATE -> Icons.Default.NetworkCheck
        else -> Icons.Default.Security
    }
}
