package ir.dekot.kavosh.core.util

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.LocationManager
import androidx.core.content.ContextCompat

/**
 * بررسی می‌کند که آیا سرویس موقعیت مکانی (GPS یا شبکه) در دستگاه روشن است یا خیر.
 */
fun isLocationEnabled(context: Context): Boolean {
    val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
    return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
            locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
}

/**
 * ابزارهای کمکی برای مدیریت پرمیشن‌ها
 * Utility functions for permission management
 */
object PermissionUtils {

    /**
     * بررسی وضعیت یک پرمیشن خاص
     * Check status of a specific permission
     */
    fun isPermissionGranted(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * بررسی وضعیت پرمیشن دسترسی به اطلاعات تلفن
     * Check phone state permission status
     */
    fun hasPhoneStatePermission(context: Context): Boolean {
        return isPermissionGranted(context, Manifest.permission.READ_PHONE_STATE)
    }

    /**
     * بررسی وضعیت پرمیشن دسترسی به موقعیت مکانی
     * Check location permission status
     */
    fun hasLocationPermission(context: Context): Boolean {
        return isPermissionGranted(context, Manifest.permission.ACCESS_FINE_LOCATION) ||
                isPermissionGranted(context, Manifest.permission.ACCESS_COARSE_LOCATION)
    }

    /**
     * بررسی وضعیت پرمیشن دسترسی به دوربین
     * Check camera permission status
     */
    fun hasCameraPermission(context: Context): Boolean {
        return isPermissionGranted(context, Manifest.permission.CAMERA)
    }

    /**
     * بررسی وضعیت پرمیشن دسترسی به Wi-Fi
     * Check Wi-Fi permission status
     */
    fun hasWifiPermission(context: Context): Boolean {
        return isPermissionGranted(context, Manifest.permission.ACCESS_WIFI_STATE)
    }

    /**
     * بررسی وضعیت پرمیشن دسترسی به شبکه
     * Check network permission status
     */
    fun hasNetworkPermission(context: Context): Boolean {
        return isPermissionGranted(context, Manifest.permission.ACCESS_NETWORK_STATE)
    }

    /**
     * بررسی اینکه آیا تمام پرمیشن‌های ضروری اعطا شده‌اند
     * Check if all essential permissions are granted
     */
    fun areEssentialPermissionsGranted(context: Context): Boolean {
        return hasPhoneStatePermission(context) &&
                hasLocationPermission(context) &&
                hasWifiPermission(context) &&
                hasNetworkPermission(context)
    }

    /**
     * دریافت لیست پرمیشن‌های اعطا نشده
     * Get list of ungranted permissions
     */
    fun getUngrantedPermissions(context: Context): List<String> {
        val ungrantedPermissions = mutableListOf<String>()

        if (!hasPhoneStatePermission(context)) {
            ungrantedPermissions.add(Manifest.permission.READ_PHONE_STATE)
        }
        if (!hasLocationPermission(context)) {
            ungrantedPermissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
        }
        if (!hasCameraPermission(context)) {
            ungrantedPermissions.add(Manifest.permission.CAMERA)
        }

        return ungrantedPermissions
    }
}