package ir.dekot.kavosh.core.util

import android.content.Context
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.size.Size
import ir.dekot.kavosh.R

/**
 * بهینه‌سازی ۶: ابزارهای بارگذاری تصاویر بهینه
 * Optimization 6: Optimized image loading utilities
 */
@Stable
object ImageLoadingUtils {

    /**
     * بهینه‌سازی ۶: کلید کش برای آیکون‌های برنامه
     * Optimization 6: Cache key for app icons
     */
    private fun getAppIconCacheKey(packageName: String): String {
        return "app_icon_$packageName"
    }

    /**
     * بهینه‌سازی ۶: دریافت آیکون برنامه با کش بهینه
     * Optimization 6: Get app icon with optimized caching
     */
    fun getAppIcon(context: Context, packageName: String): Drawable? {
        return try {
            context.packageManager.getApplicationIcon(packageName)
        } catch (e: PackageManager.NameNotFoundException) {
            null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * بهینه‌سازی ۶: ایجاد ImageRequest بهینه برای آیکون‌های برنامه
     * Optimization 6: Create optimized ImageRequest for app icons
     */
    fun createAppIconRequest(
        context: Context,
        packageName: String,
        size: Size = Size(128, 128)
    ): ImageRequest {
        return ImageRequest.Builder(context)
            .data(getAppIcon(context, packageName))
            .memoryCacheKey(getAppIconCacheKey(packageName))
            .diskCacheKey(getAppIconCacheKey(packageName))
            .size(size)
            .crossfade(true)
            .crossfade(200) // انیمیشن 200ms
            .build()
    }

    /**
     * بهینه‌سازی ۶: ایجاد ImageRequest بهینه برای تصاویر عمومی
     * Optimization 6: Create optimized ImageRequest for general images
     */
    fun createOptimizedImageRequest(
        context: Context,
        data: Any?,
        size: Size = Size.ORIGINAL,
        cacheKey: String? = null
    ): ImageRequest {
        return ImageRequest.Builder(context)
            .data(data)
            .apply {
                cacheKey?.let { key ->
                    memoryCacheKey(key)
                    diskCacheKey(key)
                }
            }
            .size(size)
            .crossfade(true)
            .crossfade(300)
            .build()
    }
}

/**
 * بهینه‌سازی ۶: Composable برای بارگذاری آیکون برنامه با کش بهینه
 * Optimization 6: Composable for loading app icon with optimized caching
 */
@Composable
fun rememberAppIconPainter(
    packageName: String,
    size: Size = Size(128, 128)
): Painter {
    val context = LocalContext.current
    
    // بهینه‌سازی ۶: remember برای جلوگیری از ایجاد مجدد request
    // Optimization 6: remember to prevent recreating request
    val imageRequest = remember(packageName, size) {
        ImageLoadingUtils.createAppIconRequest(context, packageName, size)
    }
    
    return rememberAsyncImagePainter(
        model = imageRequest,
        error = painterResource(R.drawable.ic_launcher_foreground), // آیکون پیش‌فرض
        fallback = painterResource(R.drawable.ic_launcher_foreground)
    )
}

/**
 * بهینه‌سازی ۶: Composable برای بارگذاری تصاویر عمومی با کش بهینه
 * Optimization 6: Composable for loading general images with optimized caching
 */
@Composable
fun rememberOptimizedImagePainter(
    data: Any?,
    size: Size = Size.ORIGINAL,
    cacheKey: String? = null,
    errorRes: Int? = null,
    fallbackRes: Int? = null
): Painter {
    val context = LocalContext.current
    
    // بهینه‌سازی ۶: remember برای جلوگیری از ایجاد مجدد request
    // Optimization 6: remember to prevent recreating request
    val imageRequest = remember(data, size, cacheKey) {
        ImageLoadingUtils.createOptimizedImageRequest(context, data, size, cacheKey)
    }
    
    return rememberAsyncImagePainter(
        model = imageRequest,
        error = errorRes?.let { painterResource(it) },
        fallback = fallbackRes?.let { painterResource(it) }
    )
}

/**
 * بهینه‌سازی ۶: اندازه‌های استاندارد برای تصاویر
 * Optimization 6: Standard sizes for images
 */
@Stable
object ImageSizes {
    val SMALL_ICON = Size(32, 32)
    val MEDIUM_ICON = Size(48, 48)
    val LARGE_ICON = Size(64, 64)
    val APP_ICON = Size(128, 128)
    val THUMBNAIL = Size(200, 200)
    val PREVIEW = Size(400, 400)
}

/**
 * بهینه‌سازی ۶: کلیدهای کش استاندارد
 * Optimization 6: Standard cache keys
 */
@Stable
object CacheKeys {
    fun appIcon(packageName: String) = "app_icon_$packageName"
    fun userAvatar(userId: String) = "user_avatar_$userId"
    fun deviceImage(deviceId: String) = "device_image_$deviceId"
    fun thumbnail(id: String) = "thumbnail_$id"
}
