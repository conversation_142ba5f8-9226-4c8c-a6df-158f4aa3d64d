package ir.dekot.kavosh.core.splash

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.splashscreen.SplashScreen
import ir.dekot.kavosh.feature_customeTheme.Theme
import ir.dekot.kavosh.feature_dashboard.viewModel.DashboardViewModel
import ir.dekot.kavosh.feature_deviceInfo.model.repository.SettingsRepository
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import androidx.core.content.edit

/**
 * مدیریت صفحه اسپلش بر اساس تم فعلی برنامه و بارگذاری داده‌ها
 * Manages splash screen configuration based on current app theme and data loading
 */
class SplashScreenManager(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {

    // CoroutineScope برای مدیریت کوروتین‌ها
    // CoroutineScope for managing coroutines
    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    // --- State های مربوط به بارگذاری داده‌ها ---
    private val _isDataLoading = MutableStateFlow(false)
    val isDataLoading: StateFlow<Boolean> = _isDataLoading.asStateFlow()

    private val _isDataLoadingComplete = MutableStateFlow(false)
    val isDataLoadingComplete: StateFlow<Boolean> = _isDataLoadingComplete.asStateFlow()

    /**
     * پیکربندی صفحه اسپلش بر اساس تم فعلی و شروع بارگذاری داده‌ها (اندروید 12+)
     * Configure splash screen based on current theme and start data loading (Android 12+)
     */
    @RequiresApi(Build.VERSION_CODES.S)
    fun configureSplashScreen(
        splashScreen: SplashScreen,
        activity: Activity,
        deviceInfoViewModel: DeviceInfoViewModel,
        dashboardViewModel: DashboardViewModel,
        onDataLoadingComplete: () -> Unit
    ) {
        // دریافت تم فعلی از تنظیمات
        // Get current theme from settings
        val currentTheme = getCurrentTheme()
        val isSystemDark = isSystemInDarkTheme()
        val isDarkTheme = when (currentTheme) {
            Theme.SYSTEM -> isSystemDark
            Theme.LIGHT -> false
            Theme.DARK -> true
        }

        // اعمال تنظیمات به صفحه اسپلش
        // Apply settings to splash screen
        applySplashScreenSettings(splashScreen, onDataLoadingComplete)
        // شروع بارگذاری داده‌ها
        // Start data loading
        startDataLoading(activity, deviceInfoViewModel, dashboardViewModel, onDataLoadingComplete)
    }

    /**
     * شروع بارگذاری داده‌ها برای splash screen سنتی (اندروید 11 و پایین‌تر)
     * Start data loading for traditional splash screen (Android 11 and below)
     */
    fun startDataLoadingForTraditionalSplash(
        activity: Activity,
        deviceInfoViewModel: DeviceInfoViewModel,
        dashboardViewModel: DashboardViewModel,
        onDataLoadingComplete: () -> Unit
    ) {
        // شروع بارگذاری داده‌ها
        // Start data loading
        startDataLoading(activity, deviceInfoViewModel, dashboardViewModel, onDataLoadingComplete)
    }

    /**
     * دریافت تم فعلی از SharedPreferences
     * Get current theme from SharedPreferences
     */
    private fun getCurrentTheme(): Theme {
        val sharedPrefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE)

        // First try to get theme by name (newer approach)
        val themeName = sharedPrefs.getString("theme", null)
        if (themeName != null) {
            return try {
                val theme = Theme.valueOf(themeName)
                // Migration: Convert AMOLED theme to DARK theme
                if (themeName == "AMOLED") {
                    sharedPrefs.edit { putString("theme", Theme.DARK.name) }
                    Theme.DARK
                } else {
                    theme
                }
            } catch (_: IllegalArgumentException) {
                // Handle invalid theme name
                sharedPrefs.edit { putString("theme", Theme.SYSTEM.name) }
                Theme.SYSTEM
            }
        }

        // Fallback to ordinal approach (older approach)
        val themeOrdinal = sharedPrefs.getInt("theme", Theme.SYSTEM.ordinal)
        return Theme.entries.toTypedArray().getOrElse(themeOrdinal) { Theme.SYSTEM }
    }

    /**
     * تشخیص حالت تاریک سیستم
     * Detect system dark mode
     */
    private fun isSystemInDarkTheme(): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * بهینه‌سازی ۱: شروع بارگذاری داده‌های حداقلی در پس‌زمینه
     * Optimization 1: Start minimal data loading in background
     */
    @RequiresApi(Build.VERSION_CODES.R)
    private fun startDataLoading(
        activity: Activity,
        deviceInfoViewModel: DeviceInfoViewModel,
        dashboardViewModel: DashboardViewModel,
        onDataLoadingComplete: () -> Unit
    ) {
        if (_isDataLoading.value) return

        coroutineScope.launch {
            _isDataLoading.value = true
            _isDataLoadingComplete.value = false

            // ۱. زمان شروع را ثبت کن
            val startTime = System.currentTimeMillis()

            try {
                // بهینه‌سازی: فقط داده‌های ضروری را بارگذاری می‌کنیم
                // Optimization: Load only essential data
                preloadEssentialData(activity, deviceInfoViewModel, dashboardViewModel)

                // ۲. محاسبه زمانی که گذشته است
                val elapsedTime = System.currentTimeMillis() - startTime
                // ۳. کاهش زمان انتظار برای بهبود سرعت راه‌اندازی
                val remainingTime = 800 - elapsedTime // کاهش از 2500 به 800 میلی‌ثانیه

                // ۴. اگر هنوز زمان تمام نشده، به اندازه باقی‌مانده صبر کن
                if (remainingTime > 0) {
                    delay(remainingTime)
                }

                _isDataLoadingComplete.value = true
                _isDataLoading.value = false

                // اطلاع به MainActivity که داده‌ها آماده است
                // Notify MainActivity that data is ready
                onDataLoadingComplete()

                // بهینه‌سازی: بارگذاری باقی داده‌ها در پس‌زمینه پس از نمایش UI
                // Optimization: Load remaining data in background after UI is shown
                launch {
                    loadRemainingDataInBackground(activity, deviceInfoViewModel)
                }
            } catch (_: Exception) {
                // در صورت بروز خطا، حداقل داده‌های پایه را بارگذاری می‌کنیم
                // In case of error, load at least basic data
                dashboardViewModel.loadDashboardItems()
                _isDataLoadingComplete.value = true
                _isDataLoading.value = false
                onDataLoadingComplete()
            }
        }
    }

    /**
     * بهینه‌سازی ۱: بارگذاری فقط داده‌های ضروری برای شروع سریع
     * Optimization 1: Load only essential data for fast startup
     */
    @RequiresApi(Build.VERSION_CODES.R)
    private fun preloadEssentialData(
        activity: Activity,
        deviceInfoViewModel: DeviceInfoViewModel,
        dashboardViewModel: DashboardViewModel
    ) {
        try {
            // فقط آیتم‌های داشبورد را بارگذاری می‌کنیم
            // Only load dashboard items
            dashboardViewModel.loadDashboardItems()

            // اگر کش موجود است، آن را بارگذاری می‌کنیم
            // If cache exists, load it
            if (!settingsRepository.isFirstLaunch()) {
                val cachedInfo = settingsRepository.getDeviceInfoCache()
                if (cachedInfo != null) {
                    // کش موجود است، نیازی به بارگذاری کامل نیست
                    // Cache exists, no need for full loading
                    return
                }
            }
        } catch (_: Exception) {
            // در صورت بروز خطا، حداقل داده‌های پایه را بارگذاری می‌کنیم
            // In case of error, load at least basic data
            dashboardViewModel.loadDashboardItems()
        }
    }

    /**
     * بهینه‌سازی ۱: بارگذاری باقی داده‌ها در پس‌زمینه پس از نمایش UI
     * Optimization 1: Load remaining data in background after UI is shown
     */
    @RequiresApi(Build.VERSION_CODES.R)
    private suspend fun loadRemainingDataInBackground(
        activity: Activity,
        deviceInfoViewModel: DeviceInfoViewModel
    ) {
        try {
            // بارگذاری اطلاعات دستگاه در پس‌زمینه
            // Load device information in background
            if (settingsRepository.isFirstLaunch()) {
                // برای اولین اجرا، مستقیماً داده‌ها را بارگذاری می‌کنیم
                // For first launch, directly load data
                deviceInfoViewModel.loadDataForFirstLaunch(activity)
            } else {
                // برای اجراهای بعدی، اطمینان از بارگذاری کامل داده‌ها
                // For subsequent launches, ensure complete data loading
                deviceInfoViewModel.ensureDataIsLoaded(activity)
            }

            // بارگذاری اطلاعات برنامه‌ها در پس‌زمینه
            // Load apps information in background
            deviceInfoViewModel.loadAppsListIfNeeded()
        } catch (_: Exception) {
            // در صورت بروز خطا، لاگ می‌کنیم اما UI را مختل نمی‌کنیم
            // In case of error, log but don't disrupt UI
        }
    }

    /**
     * اعمال تنظیمات به صفحه اسپلش
     * Apply settings to splash screen
     */
    private fun applySplashScreenSettings(
        splashScreen: SplashScreen,
        onDataLoadingComplete: () -> Unit
    ) {
        // صفحه اسپلش را تا زمان تکمیل بارگذاری داده‌ها نگه می‌داریم
        // Keep splash screen until data loading is complete
        splashScreen.setKeepOnScreenCondition {
            !_isDataLoadingComplete.value
        }

        // تنظیم انیمیشن خروج نرم
        // Set smooth exit animation
        splashScreen.setOnExitAnimationListener { splashScreenView ->
            // انیمیشن محو شدن نرم
            // Smooth fade out animation
            splashScreenView.iconView.animate()
                ?.alpha(0f)
                ?.setDuration(300)
                ?.withEndAction {
                    splashScreenView.remove()
                }
        }
    }

    /**
     * ریست کردن وضعیت بارگذاری
     * Reset loading state
     */
    fun resetLoadingState() {
        _isDataLoading.value = false
        _isDataLoadingComplete.value = false
    }

    /**
     * پاک کردن منابع
     * Clean up resources
     */
    fun cleanup() {
        coroutineScope.coroutineContext.job.cancel()
    }

    companion object {
        /**
         * ایجاد نمونه از SplashScreenManager
         * Create instance of SplashScreenManager
         */
        fun create(context: Context, settingsRepository: SettingsRepository): SplashScreenManager {
            return SplashScreenManager(context, settingsRepository)
        }
    }
}
