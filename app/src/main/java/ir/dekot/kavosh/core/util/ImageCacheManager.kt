@file:OptIn(ExperimentalCoilApi::class)

package ir.dekot.kavosh.core.util

import android.content.Context
import coil.ImageLoader
import coil.annotation.ExperimentalCoilApi
import coil.memory.MemoryCache
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * بهینه‌سازی ۶: مدیریت کش تصاویر
 * Optimization 6: Image cache management
 */
@Singleton
class ImageCacheManager @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val imageLoader: ImageLoader
) {

    /**
     * بهینه‌سازی ۶: محاسبه اندازه کش حافظه
     * Optimization 6: Calculate memory cache size
     */
    fun getMemoryCacheSize(): Long {
        return (imageLoader.memoryCache?.size ?: 0L).toLong()
    }

    /**
     * بهینه‌سازی ۶: محاسبه حداکثر اندازه کش حافظه
     * Optimization 6: Calculate max memory cache size
     */
    fun getMaxMemoryCacheSize(): Long {
        return (imageLoader.memoryCache?.maxSize ?: 0L).toLong()
    }

    /**
     * بهینه‌سازی ۶: محاسبه اندازه کش دیسک
     * Optimization 6: Calculate disk cache size
     */
    suspend fun getDiskCacheSize(): Long = withContext(Dispatchers.IO) {
        return@withContext imageLoader.diskCache?.size ?: 0L
    }

    /**
     * بهینه‌سازی ۶: محاسبه حداکثر اندازه کش دیسک
     * Optimization 6: Calculate max disk cache size
     */
    suspend fun getMaxDiskCacheSize(): Long = withContext(Dispatchers.IO) {
        return@withContext imageLoader.diskCache?.maxSize ?: 0L
    }

    /**
     * بهینه‌سازی ۶: پاک کردن کش حافظه
     * Optimization 6: Clear memory cache
     */
    fun clearMemoryCache() {
        imageLoader.memoryCache?.clear()
    }

    /**
     * بهینه‌سازی ۶: پاک کردن کش دیسک
     * Optimization 6: Clear disk cache
     */
    suspend fun clearDiskCache() = withContext(Dispatchers.IO) {
        imageLoader.diskCache?.clear()
    }

    /**
     * بهینه‌سازی ۶: پاک کردن تمام کش‌ها
     * Optimization 6: Clear all caches
     */
    suspend fun clearAllCaches() {
        clearMemoryCache()
        clearDiskCache()
    }

    /**
     * بهینه‌سازی ۶: حذف آیتم خاص از کش حافظه
     * Optimization 6: Remove specific item from memory cache
     */
    fun removeFromMemoryCache(key: String) {
        imageLoader.memoryCache?.remove(MemoryCache.Key(key))
    }

    /**
     * بهینه‌سازی ۶: حذف آیتم خاص از کش دیسک
     * Optimization 6: Remove specific item from disk cache
     */
    suspend fun removeFromDiskCache(key: String) = withContext(Dispatchers.IO) {
        imageLoader.diskCache?.remove(key)
    }

    /**
     * بهینه‌سازی ۶: بررسی وجود آیتم در کش حافظه
     * Optimization 6: Check if item exists in memory cache
     */
    fun isInMemoryCache(key: String): Boolean {
        return imageLoader.memoryCache?.get(MemoryCache.Key(key)) != null
    }

    /**
     * بهینه‌سازی ۶: بررسی وجود آیتم در کش دیسک
     * Optimization 6: Check if item exists in disk cache
     */
    suspend fun isInDiskCache(key: String): Boolean = withContext(Dispatchers.IO) {
        return@withContext imageLoader.diskCache?.openSnapshot(key) != null
    }

    /**
     * بهینه‌سازی ۶: دریافت آمار کش
     * Optimization 6: Get cache statistics
     */
    suspend fun getCacheStats(): ImageCacheStats = withContext(Dispatchers.IO) {
        val memorySize = getMemoryCacheSize()
        val maxMemorySize = getMaxMemoryCacheSize()
        val diskSize = getDiskCacheSize()
        val maxDiskSize = getMaxDiskCacheSize()

        return@withContext ImageCacheStats(
            memoryCacheSize = memorySize,
            maxMemoryCacheSize = maxMemorySize,
            diskCacheSize = diskSize,
            maxDiskCacheSize = maxDiskSize,
            memoryCacheHitRate = calculateMemoryCacheHitRate(),
            diskCacheHitRate = calculateDiskCacheHitRate()
        )
    }

    /**
     * بهینه‌سازی ۶: محاسبه نرخ موفقیت کش حافظه
     * Optimization 6: Calculate memory cache hit rate
     */
    private fun calculateMemoryCacheHitRate(): Float {
        // این محاسبه تقریبی است چون Coil آمار دقیق ارائه نمی‌دهد
        // This is an approximation as Coil doesn't provide exact statistics
        val currentSize = getMemoryCacheSize()
        val maxSize = getMaxMemoryCacheSize()
        return if (maxSize > 0) (currentSize.toFloat() / maxSize.toFloat()) else 0f
    }

    /**
     * بهینه‌سازی ۶: محاسبه نرخ موفقیت کش دیسک
     * Optimization 6: Calculate disk cache hit rate
     */
    private suspend fun calculateDiskCacheHitRate(): Float {
        // این محاسبه تقریبی است چون Coil آمار دقیق ارائه نمی‌دهد
        // This is an approximation as Coil doesn't provide exact statistics
        val currentSize = getDiskCacheSize()
        val maxSize = getMaxDiskCacheSize()
        return if (maxSize > 0) (currentSize.toFloat() / maxSize.toFloat()) else 0f
    }

    /**
     * بهینه‌سازی ۶: پاکسازی هوشمند کش
     * Optimization 6: Smart cache cleanup
     */
    suspend fun performSmartCleanup() = withContext(Dispatchers.IO) {
        val stats = getCacheStats()
        
        // اگر کش حافظه بیش از 80% پر باشد، آن را پاک کن
        // If memory cache is more than 80% full, clear it
        if (stats.memoryCacheUsagePercent > 0.8f) {
            clearMemoryCache()
        }
        
        // اگر کش دیسک بیش از 90% پر باشد، آن را پاک کن
        // If disk cache is more than 90% full, clear it
        if (stats.diskCacheUsagePercent > 0.9f) {
            clearDiskCache()
        }
    }

    /**
     * بهینه‌سازی ۶: فرمت کردن اندازه برای نمایش
     * Optimization 6: Format size for display
     */
    fun formatCacheSize(sizeBytes: Long): String {
        return when {
            sizeBytes < 1024 -> "$sizeBytes B"
            sizeBytes < 1024 * 1024 -> "${sizeBytes / 1024} KB"
            sizeBytes < 1024 * 1024 * 1024 -> "${sizeBytes / (1024 * 1024)} MB"
            else -> "${sizeBytes / (1024 * 1024 * 1024)} GB"
        }
    }
}

/**
 * بهینه‌سازی ۶: آمار کش تصاویر
 * Optimization 6: Image cache statistics
 */
data class ImageCacheStats(
    val memoryCacheSize: Long,
    val maxMemoryCacheSize: Long,
    val diskCacheSize: Long,
    val maxDiskCacheSize: Long,
    val memoryCacheHitRate: Float,
    val diskCacheHitRate: Float
) {
    val memoryCacheUsagePercent: Float
        get() = if (maxMemoryCacheSize > 0) memoryCacheSize.toFloat() / maxMemoryCacheSize.toFloat() else 0f
    
    val diskCacheUsagePercent: Float
        get() = if (maxDiskCacheSize > 0) diskCacheSize.toFloat() / maxDiskCacheSize.toFloat() else 0f
    
    val totalCacheSize: Long
        get() = memoryCacheSize + diskCacheSize
    
    val maxTotalCacheSize: Long
        get() = maxMemoryCacheSize + maxDiskCacheSize
}
