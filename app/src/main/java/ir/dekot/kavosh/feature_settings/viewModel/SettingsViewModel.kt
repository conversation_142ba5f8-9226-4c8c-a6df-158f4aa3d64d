package ir.dekot.kavosh.feature_settings.viewModel

import android.app.Activity
import android.content.Context
import androidx.compose.ui.geometry.Offset
import ir.dekot.kavosh.core.util.LanguageManager
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import ir.dekot.kavosh.core.util.ImageCacheManager
import ir.dekot.kavosh.core.util.ImageCacheStats
import ir.dekot.kavosh.feature_customeTheme.ColorTheme
import ir.dekot.kavosh.feature_customeTheme.CustomColorTheme
import ir.dekot.kavosh.feature_customeTheme.PredefinedColorTheme
import ir.dekot.kavosh.feature_customeTheme.Theme
import ir.dekot.kavosh.feature_deviceInfo.model.repository.SettingsRepository
import ir.dekot.kavosh.feature_deviceInfo.model.repository.SystemRepository


import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow


import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository,
    private val systemRepository: SystemRepository,
    // بهینه‌سازی ۶: اضافه کردن ImageCacheManager
    // Optimization 6: Adding ImageCacheManager
    private val imageCacheManager: ImageCacheManager,
) : ViewModel() {

    // --- State های مربوط به تنظیمات ---
    private val _themeState = MutableStateFlow(Theme.SYSTEM)
    val themeState: StateFlow<Theme> = _themeState.asStateFlow()

    private val _isReorderingEnabled = MutableStateFlow(true)
    val isReorderingEnabled: StateFlow<Boolean> = _isReorderingEnabled.asStateFlow()

    private val _appVersion = MutableStateFlow("")
    val appVersion: StateFlow<String> = _appVersion.asStateFlow()

    private val _language = MutableStateFlow(settingsRepository.getLanguage())
    val language: StateFlow<String> = _language.asStateFlow()

    private val _languageChangeRequest = MutableSharedFlow<String>()
    val languageChangeRequest = _languageChangeRequest.asSharedFlow()

    // --- State های جدید برای تم‌های رنگی ---
    private val _currentColorTheme = MutableStateFlow<ColorTheme?>(null)
    val currentColorTheme: StateFlow<ColorTheme?> = _currentColorTheme.asStateFlow()

    // --- State های مربوط به انیمیشن تغییر تم ---
    private val _themeTransitionState = MutableStateFlow<ThemeTransitionState?>(null)
    val themeTransitionState: StateFlow<ThemeTransitionState?> = _themeTransitionState.asStateFlow()

    /**
     * مدل برای مدیریت حالت انیمیشن تغییر تم
     */
    data class ThemeTransitionState(
        val newTheme: Theme,
        val startPoint: Offset,
        val isAnimating: Boolean = false
    )

    init {
        // بهینه‌سازی ۱: بارگذاری تنبل تنظیمات
        // Optimization 1: Lazy loading of settings
        loadSettingsLazily()
    }

    /**
     * بهینه‌سازی ۱: بارگذاری تنبل تنظیمات برای بهبود سرعت راه‌اندازی
     * Optimization 1: Lazy settings loading for improved startup speed
     */
    private fun loadSettingsLazily() {
        viewModelScope.launch {
            // بارگذاری تنظیمات در پس‌زمینه
            // Load settings in background
            _themeState.value = settingsRepository.getTheme()
            _isReorderingEnabled.value = settingsRepository.isReorderingEnabled()
            // زبان از constructor خوانده شده، نیازی به بارگذاری مجدد نیست
            // Language already loaded in constructor, no need to reload
            _appVersion.value = systemRepository.getAppVersion()
            _currentColorTheme.value = settingsRepository.getCurrentColorTheme()
        }
    }

    // --- توابع مربوط به رویدادهای کاربر ---

    fun onLanguageSelected(lang: String) {
        // اگر زبان تغییری نکرده، کاری انجام نده
        if (lang == _language.value) return

        // تغییر فوری state برای UI (RTL/LTR)
        _language.value = lang

        // ذخیره در پس‌زمینه
        viewModelScope.launch {
            settingsRepository.saveLanguage(lang)
            // ارسال رویداد برای تغییر سریع زبان
            _languageChangeRequest.emit(lang)
        }
    }

    fun onThemeSelected(theme: Theme) {
        _themeState.value = theme
        viewModelScope.launch {
            settingsRepository.saveTheme(theme)
        }
    }

    /**
     * شروع انیمیشن تغییر تم با دایره‌ای از نقطه مشخص
     */
    fun startThemeTransition(theme: Theme, startPoint: Offset) {
        if (theme == _themeState.value) return // اگر تم تغییری نکرده، کاری انجام نده

        _themeTransitionState.value = ThemeTransitionState(
            newTheme = theme,
            startPoint = startPoint,
            isAnimating = true
        )
    }

    /**
     * اعمال تم جدید در وسط انیمیشن (پس از 150ms)
     */
    fun applyThemeAfterDelay() {
        val transitionState = _themeTransitionState.value
        if (transitionState != null) {
            _themeState.value = transitionState.newTheme
            viewModelScope.launch {
                settingsRepository.saveTheme(transitionState.newTheme)
            }
        }
    }

    /**
     * پایان انیمیشن تغییر تم
     */
    fun finishThemeTransition() {
        _themeTransitionState.value = null
    }

    fun onReorderingToggled(enabled: Boolean) {
        _isReorderingEnabled.value = enabled
        viewModelScope.launch {
            settingsRepository.setReorderingEnabled(enabled)
        }
    }

    /**
     * بهینه‌سازی ۴: پاک کردن کش اطلاعات دستگاه با مدیریت پیشرفته
     * Optimization 4: Clear device info cache with advanced management
     */
    fun clearCache() {
        viewModelScope.launch {
            settingsRepository.clearDeviceInfoCache()
        }
    }

    /**
     * بهینه‌سازی ۴: پاک کردن تمام کش‌ها
     * Optimization 4: Clear all caches
     */
    fun clearAllCaches() {
        viewModelScope.launch {
            settingsRepository.clearAllCaches()
        }
    }

    /**
     * بهینه‌سازی ۴: پاکسازی کش‌های منقضی شده
     * Optimization 4: Clean up expired caches
     */
    fun cleanupExpiredCaches() {
        viewModelScope.launch {
            settingsRepository.cleanupExpiredCaches()
        }
    }

    /**
     * بهینه‌سازی ۴: دریافت اندازه کش
     * Optimization 4: Get cache size
     */
    fun getCacheSizeFormatted(): String {
        val sizeBytes = settingsRepository.getCacheSizeBytes()
        return when {
            sizeBytes < 1024 -> "$sizeBytes B"
            sizeBytes < 1024 * 1024 -> "${sizeBytes / 1024} KB"
            else -> "${sizeBytes / (1024 * 1024)} MB"
        }
    }

    /**
     * بهینه‌سازی ۴: پاکسازی هوشمند کش
     * Optimization 4: Smart cache cleanup
     */
    fun performSmartCacheCleanup() {
        viewModelScope.launch {
            if (settingsRepository.isCacheCleanupNeeded()) {
                settingsRepository.performSmartCacheCleanup()
            }
        }
    }

    /**
     * بازنشانی همه تنظیمات به حالت پیش‌فرض
     */
    fun resetAllSettings() {
        // تغییر فوری Locale برای زبان فارسی
        val locale = java.util.Locale("fa")
        java.util.Locale.setDefault(locale)

        // تغییر فوری state ها برای UI
        _themeState.value = Theme.SYSTEM
        _language.value = "fa"
        _isReorderingEnabled.value = true

        // عملیات پس‌زمینه (ذخیره)
        viewModelScope.launch {
            // بازنشانی تم به حالت سیستم
            settingsRepository.saveTheme(Theme.SYSTEM)

            // بازنشانی زبان به فارسی
            settingsRepository.saveLanguage("fa")

            // بازنشانی قابلیت جابجایی به فعال
            settingsRepository.setReorderingEnabled(true)

            // پاک کردن کش
            settingsRepository.clearDeviceInfoCache()
        }
    }

    // --- توابع جدید برای مدیریت تم‌های رنگی ---

    /**
     * انتخاب تم رنگی از پیش تعریف شده
     */
    fun selectPredefinedColorTheme(colorTheme: PredefinedColorTheme) {
        viewModelScope.launch {
            settingsRepository.savePredefinedColorTheme(colorTheme)
            _currentColorTheme.value = colorTheme.toColorTheme()
        }
    }

    /**
     * انتخاب تم رنگی سفارشی
     */
    fun selectCustomColorTheme(customTheme: CustomColorTheme) {
        viewModelScope.launch {
            settingsRepository.saveCustomColorTheme(customTheme)
            _currentColorTheme.value = customTheme.toColorTheme()
        }
    }

    /**
     * بازنشانی تم رنگی به حالت پیش‌فرض
     */
    fun resetColorTheme() {
        viewModelScope.launch {
            settingsRepository.resetColorTheme()
            _currentColorTheme.value = null
        }
    }

    /**
     * دریافت لیست تم‌های رنگی از پیش تعریف شده
     */
    fun getPredefinedColorThemes(): List<PredefinedColorTheme> {
        return PredefinedColorTheme.entries
    }

    /**
     * بررسی اینکه آیا تم رنگی سفارشی انتخاب شده یا خیر
     */
    fun hasCustomColorTheme(): Boolean {
        return settingsRepository.hasCustomColorTheme()
    }

    // بهینه‌سازی ۶: متدهای مدیریت کش تصاویر
    // Optimization 6: Image cache management methods

    /**
     * بهینه‌سازی ۶: دریافت آمار کش تصاویر
     * Optimization 6: Get image cache statistics
     */
    suspend fun getImageCacheStats(): ImageCacheStats {
        return imageCacheManager.getCacheStats()
    }

    /**
     * بهینه‌سازی ۶: پاک کردن کش تصاویر حافظه
     * Optimization 6: Clear image memory cache
     */
    fun clearImageMemoryCache() {
        imageCacheManager.clearMemoryCache()
    }

    /**
     * بهینه‌سازی ۶: پاک کردن کش تصاویر دیسک
     * Optimization 6: Clear image disk cache
     */
    fun clearImageDiskCache() {
        viewModelScope.launch {
            imageCacheManager.clearDiskCache()
        }
    }

    /**
     * بهینه‌سازی ۶: پاک کردن تمام کش‌های تصاویر
     * Optimization 6: Clear all image caches
     */
    fun clearAllImageCaches() {
        viewModelScope.launch {
            imageCacheManager.clearAllCaches()
        }
    }

    /**
     * بهینه‌سازی ۶: فرمت کردن اندازه کش تصاویر
     * Optimization 6: Format image cache size
     */
    fun formatImageCacheSize(sizeBytes: Long): String {
        return imageCacheManager.formatCacheSize(sizeBytes)
    }

    companion object {
        /**
         * متد استاتیک برای دسترسی به زبان قبل از اینکه ViewModel ساخته شود.
         * این متد برای `attachBaseContext` در MainActivity ضروری است.
         */
        fun getSavedLanguage(context: Context): String {
            val prefs = context.getSharedPreferences("device_inspector_prefs", Context.MODE_PRIVATE)
            return prefs.getString("app_language", "fa") ?: "fa"
        }
    }
}