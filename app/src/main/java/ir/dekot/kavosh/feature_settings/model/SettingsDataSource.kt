package ir.dekot.kavosh.feature_settings.model

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.content.edit
import dagger.hilt.android.qualifiers.ApplicationContext
import ir.dekot.kavosh.feature_customeTheme.ColorTheme
import ir.dekot.kavosh.feature_customeTheme.CustomColorTheme
import ir.dekot.kavosh.feature_customeTheme.PredefinedColorTheme
import ir.dekot.kavosh.feature_customeTheme.Theme
import ir.dekot.kavosh.feature_deviceInfo.model.AppInfo
import ir.dekot.kavosh.feature_deviceInfo.model.DeviceInfo
import ir.dekot.kavosh.feature_deviceInfo.model.InfoCategory
import ir.dekot.kavosh.feature_testing.model.HealthCheckSummary
import ir.dekot.kavosh.feature_testing.model.PerformanceScore
import ir.dekot.kavosh.feature_testing.model.StorageTestSummary
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsDataSource @Inject constructor(@ApplicationContext context: Context) {

    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences("device_inspector_prefs", Context.MODE_PRIVATE)
    }

    // کلیدهای SharedPreferences
    private companion object {
        const val KEY_DEVICE_INFO_CACHE = "device_info_cache"
        const val KEY_THEME = "app_theme"
        const val KEY_FIRST_LAUNCH = "is_first_launch"
        const val KEY_DASHBOARD_ORDER = "dashboard_order"
        const val KEY_HIDDEN_CATEGORIES = "hidden_categories"
        const val KEY_DASHBOARD_REORDER_ENABLED = "dashboard_reorder_enabled"
        // کلید جدید برای زبان
        const val KEY_APP_LANGUAGE = "app_language"
        const val KEY_USER_APPS_CACHE = "user_apps_cache"
        const val KEY_SYSTEM_APPS_CACHE = "system_apps_cache"
        const val KEY_PACKAGE_COUNT = "package_count"
        // کلیدهای جدید برای ذخیره تاریخچه تست‌های تشخیصی
        const val KEY_HEALTH_CHECK_HISTORY = "health_check_history"

        // بهینه‌سازی ۴: کلیدهای جدید برای TTL-based caching
        // Optimization 4: New keys for TTL-based caching
        const val KEY_DEVICE_INFO_CACHE_TIMESTAMP = "device_info_cache_timestamp"
        const val KEY_USER_APPS_CACHE_TIMESTAMP = "user_apps_cache_timestamp"
        const val KEY_SYSTEM_APPS_CACHE_TIMESTAMP = "system_apps_cache_timestamp"
        const val KEY_CACHE_VERSION = "cache_version"

        // بهینه‌سازی ۴: TTL مقادیر (در میلی‌ثانیه)
        // Optimization 4: TTL values (in milliseconds)
        const val DEVICE_INFO_CACHE_TTL = 24 * 60 * 60 * 1000L // 24 ساعت
        const val APPS_CACHE_TTL = 6 * 60 * 60 * 1000L // 6 ساعت
        const val CURRENT_CACHE_VERSION = 1
        const val KEY_PERFORMANCE_SCORE_HISTORY = "performance_score_history"
        const val KEY_STORAGE_SPEED_TEST_HISTORY = "storage_speed_test_history"
        // کلیدهای جدید برای تم‌های رنگی
        const val KEY_COLOR_THEME_TYPE = "color_theme_type" // "predefined" یا "custom"
        const val KEY_PREDEFINED_COLOR_THEME = "predefined_color_theme"
        const val KEY_CUSTOM_PRIMARY_COLOR = "custom_primary_color"
        const val KEY_CUSTOM_SECONDARY_COLOR = "custom_secondary_color"
    }

    // بهینه‌سازی ۴: متدهای جدید برای کش برنامه‌ها با TTL
    // Optimization 4: New methods for apps cache with TTL

    fun saveAppsCache(userApps: List<AppInfo>, systemApps: List<AppInfo>, count: Int) {
        val currentTime = System.currentTimeMillis()
        prefs.edit {
            putString(KEY_USER_APPS_CACHE, Json.Default.encodeToString(userApps))
            putString(KEY_SYSTEM_APPS_CACHE, Json.Default.encodeToString(systemApps))
            putInt(KEY_PACKAGE_COUNT, count)
            // بهینه‌سازی ۴: ذخیره timestamp برای TTL
            // Optimization 4: Save timestamp for TTL
            putLong(KEY_USER_APPS_CACHE_TIMESTAMP, currentTime)
            putLong(KEY_SYSTEM_APPS_CACHE_TIMESTAMP, currentTime)
            putInt(KEY_CACHE_VERSION, CURRENT_CACHE_VERSION)
        }
    }

    /**
     * بهینه‌سازی ۴: دریافت کش برنامه‌های کاربری با بررسی TTL
     * Optimization 4: Get user apps cache with TTL validation
     */
    fun getUserAppsCache(): List<AppInfo>? {
        if (!isAppsCacheValid()) return null

        val jsonString = prefs.getString(KEY_USER_APPS_CACHE, null)
        return jsonString?.let {
            try {
                Json.Default.decodeFromString<List<AppInfo>>(it)
            } catch (_: Exception) {
                // در صورت خطا در پارس، کش را پاک می‌کنیم
                // Clear cache on parse error
                clearAppsCache()
                null
            }
        }
    }

    /**
     * بهینه‌سازی ۴: دریافت کش برنامه‌های سیستمی با بررسی TTL
     * Optimization 4: Get system apps cache with TTL validation
     */
    fun getSystemAppsCache(): List<AppInfo>? {
        if (!isAppsCacheValid()) return null

        val jsonString = prefs.getString(KEY_SYSTEM_APPS_CACHE, null)
        return jsonString?.let {
            try {
                Json.Default.decodeFromString<List<AppInfo>>(it)
            } catch (_: Exception) {
                // در صورت خطا در پارس، کش را پاک می‌کنیم
                // Clear cache on parse error
                clearAppsCache()
                null
            }
        }
    }

    /**
     * بهینه‌سازی ۴: دریافت تعداد پکیج‌ها از کش با بررسی TTL
     * Optimization 4: Get package count from cache with TTL validation
     */
    fun getPackageCountCache(): Int {
        return if (isAppsCacheValid()) {
            prefs.getInt(KEY_PACKAGE_COUNT, -1)
        } else {
            -1 // کش منقضی شده است
        }
    }

    /**
     * بهینه‌سازی ۴: بررسی اعتبار کش برنامه‌ها
     * Optimization 4: Validate apps cache TTL
     */
    private fun isAppsCacheValid(): Boolean {
        val currentTime = System.currentTimeMillis()
        val cacheTime = prefs.getLong(KEY_USER_APPS_CACHE_TIMESTAMP, 0)
        val cacheVersion = prefs.getInt(KEY_CACHE_VERSION, 0)

        // بررسی نسخه کش و TTL
        // Check cache version and TTL
        return cacheVersion == CURRENT_CACHE_VERSION &&
               cacheTime > 0 &&
               (currentTime - cacheTime) < APPS_CACHE_TTL
    }

    /**
     * بهینه‌سازی ۴: پاک کردن کش برنامه‌ها
     * Optimization 4: Clear apps cache
     */
    fun clearAppsCache() {
        prefs.edit {
            remove(KEY_USER_APPS_CACHE)
            remove(KEY_SYSTEM_APPS_CACHE)
            remove(KEY_PACKAGE_COUNT)
            remove(KEY_USER_APPS_CACHE_TIMESTAMP)
            remove(KEY_SYSTEM_APPS_CACHE_TIMESTAMP)
        }
    }

    // --- متدهای جدید برای ذخیره و بازیابی تاریخچه تست‌های تشخیصی ---

    /**
     * ذخیره تاریخچه بررسی سلامت
     */
    fun saveHealthCheckHistory(history: List<HealthCheckSummary>) {
        val jsonString = Json.Default.encodeToString(history)
        prefs.edit {
            putString(KEY_HEALTH_CHECK_HISTORY, jsonString)
        }
    }

    /**
     * بازیابی تاریخچه بررسی سلامت
     */
    fun getHealthCheckHistory(): List<HealthCheckSummary> {
        val jsonString = prefs.getString(KEY_HEALTH_CHECK_HISTORY, null)
        return jsonString?.let {
            try {
                Json.Default.decodeFromString<List<HealthCheckSummary>>(it)
            } catch (e: Exception) {
                e.printStackTrace()
                emptyList()
            }
        } ?: emptyList()
    }

    /**
     * ذخیره تاریخچه امتیاز عملکرد
     */
    fun savePerformanceScoreHistory(history: List<PerformanceScore>) {
        val jsonString = Json.Default.encodeToString(history)
        prefs.edit {
            putString(KEY_PERFORMANCE_SCORE_HISTORY, jsonString)
        }
    }

    /**
     * بازیابی تاریخچه امتیاز عملکرد
     */
    fun getPerformanceScoreHistory(): List<PerformanceScore> {
        val jsonString = prefs.getString(KEY_PERFORMANCE_SCORE_HISTORY, null)
        return jsonString?.let {
            try {
                Json.Default.decodeFromString<List<PerformanceScore>>(it)
            } catch (e: Exception) {
                e.printStackTrace()
                emptyList()
            }
        } ?: emptyList()
    }



    /**
     * ذخیره تاریخچه تست سرعت حافظه
     */
    fun saveStorageSpeedTestHistory(history: List<StorageTestSummary>) {
        val jsonString = Json.Default.encodeToString(history)
        prefs.edit {
            putString(KEY_STORAGE_SPEED_TEST_HISTORY, jsonString)
        }
    }

    /**
     * بازیابی تاریخچه تست سرعت حافظه
     */
    fun getStorageSpeedTestHistory(): List<StorageTestSummary> {
        val jsonString = prefs.getString(KEY_STORAGE_SPEED_TEST_HISTORY, null)
        return jsonString?.let {
            try {
                Json.Default.decodeFromString<List<StorageTestSummary>>(it)
            } catch (e: Exception) {
                e.printStackTrace()
                emptyList()
            }
        } ?: emptyList()
    }

    // بهینه‌سازی ۴: متدهای جدید برای مدیریت کش با TTL
    // Optimization 4: New methods for cache management with TTL

    /**
     * بهینه‌سازی ۴: ذخیره کش اطلاعات دستگاه با timestamp
     * Optimization 4: Save device info cache with timestamp
     */
    fun saveDeviceInfoCache(deviceInfo: DeviceInfo) {
        val jsonString = Json.Default.encodeToString(deviceInfo)
        val currentTime = System.currentTimeMillis()
        prefs.edit {
            putString(KEY_DEVICE_INFO_CACHE, jsonString)
            putLong(KEY_DEVICE_INFO_CACHE_TIMESTAMP, currentTime)
            putInt(KEY_CACHE_VERSION, CURRENT_CACHE_VERSION)
        }
    }

    /**
     * بهینه‌سازی ۴: دریافت کش اطلاعات دستگاه با بررسی TTL
     * Optimization 4: Get device info cache with TTL validation
     */
    fun getDeviceInfoCache(): DeviceInfo? {
        if (!isDeviceInfoCacheValid()) return null

        val jsonString = prefs.getString(KEY_DEVICE_INFO_CACHE, null)
        return jsonString?.let {
            try {
                Json.Default.decodeFromString<DeviceInfo>(it)
            } catch (e: Exception) {
                e.printStackTrace()
                // در صورت خطا در پارس کردن، کش را پاک می‌کنیم
                // Clear cache on parse error
                clearDeviceInfoCache()
                null
            }
        }
    }

    /**
     * بهینه‌سازی ۴: بررسی اعتبار کش اطلاعات دستگاه
     * Optimization 4: Validate device info cache TTL
     */
    private fun isDeviceInfoCacheValid(): Boolean {
        val currentTime = System.currentTimeMillis()
        val cacheTime = prefs.getLong(KEY_DEVICE_INFO_CACHE_TIMESTAMP, 0)
        val cacheVersion = prefs.getInt(KEY_CACHE_VERSION, 0)

        // بررسی نسخه کش و TTL
        // Check cache version and TTL
        return cacheVersion == CURRENT_CACHE_VERSION &&
               cacheTime > 0 &&
               (currentTime - cacheTime) < DEVICE_INFO_CACHE_TTL
    }

    /**
     * بهینه‌سازی ۴: پاک کردن کش اطلاعات دستگاه
     * Optimization 4: Clear device info cache
     */
    fun clearDeviceInfoCache() {
        prefs.edit {
            remove(KEY_DEVICE_INFO_CACHE)
            remove(KEY_DEVICE_INFO_CACHE_TIMESTAMP)
        }
    }

    // ... (متدهای دیگر بدون تغییر) ...

    /**
     * زبان انتخاب شده توسط کاربر را (به صورت تگ زبان مثل "fa" یا "en") ذخیره می‌کند.
     */
    fun saveLanguage(language: String) {
        // استفاده از apply() برای عملیات async و سریع
        prefs.edit {
            putString(KEY_APP_LANGUAGE, language)
        }
    }

    /**
     * آخرین زبان ذخیره شده را می‌خواند.
     * @return در صورتی که زبانی ذخیره نشده باشد، زبان پیش‌فرض دستگاه را برمی‌گرداند.
     */
    fun getLanguage(): String {
        return prefs.getString(KEY_APP_LANGUAGE, "fa") ?: "fa"
    }

    fun isFirstLaunch(): Boolean {
        return prefs.getBoolean(KEY_FIRST_LAUNCH, true)
    }

    fun setFirstLaunchCompleted() {
        prefs.edit { putBoolean(KEY_FIRST_LAUNCH, false) }
    }

    /**
     * تم انتخاب شده توسط کاربر را ذخیره می‌کند.
     */
    fun saveTheme(theme: Theme) {
        prefs.edit {
            putString(KEY_THEME, theme.name)
        }
    }

    /**
     * آخرین تم ذخیره شده را می‌خواند.
     * @return مقدار Theme.SYSTEM به عنوان پیش‌فرض.
     */
    fun getTheme(): Theme {
        val themeName = prefs.getString(KEY_THEME, Theme.SYSTEM.name)
        return try {
            val theme = Theme.valueOf(themeName ?: Theme.SYSTEM.name)
            // Migration: Convert AMOLED theme to DARK theme
            if (themeName == "AMOLED") {
                saveTheme(Theme.DARK)
                Theme.DARK
            } else {
                theme
            }
        } catch (_: IllegalArgumentException) {
            // Handle case where theme name is invalid (e.g., AMOLED after removal)
            saveTheme(Theme.SYSTEM)
            Theme.SYSTEM
        }
    }

    /**
     * ترتیب آیتم‌های داشبورد را به صورت یک رشته جدا شده با کاما ذخیره می‌کند.
     */
    fun saveDashboardOrder(categories: List<InfoCategory>) {
        val orderString = categories.joinToString(",") { it.name }
        prefs.edit {
            putString(KEY_DASHBOARD_ORDER, orderString)
        }
    }

    /**
     * ترتیب ذخیره شده آیتم‌های داشبورد را بازخوانی می‌کند.
     * اگر ترتیبی ذخیره نشده باشد، ترتیب پیش‌فرض را برمی‌گرداند.
     */
    fun getDashboardOrder(): List<InfoCategory> {
        val defaultOrder = InfoCategory.entries.joinToString(",") { it.name }
        val orderString = prefs.getString(KEY_DASHBOARD_ORDER, defaultOrder) ?: defaultOrder
        return orderString.split(",").mapNotNull { try { InfoCategory.valueOf(it) } catch (_: Exception) { null } }
    }

    /**
     * مجموعه‌ای از دسته‌بندی‌های مخفی را ذخیره می‌کند.
     */
    fun saveHiddenCategories(hidden: Set<InfoCategory>) {
        val hiddenSetString = hidden.map { it.name }.toSet()
        prefs.edit {
            putStringSet(KEY_HIDDEN_CATEGORIES, hiddenSetString)
        }
    }

    /**
     * دسته‌بندی‌های مخفی شده را بازخوانی می‌کند.
     */
    fun getHiddenCategories(): Set<InfoCategory> {
        val hiddenSetString = prefs.getStringSet(KEY_HIDDEN_CATEGORIES, emptySet()) ?: emptySet()
        return hiddenSetString.mapNotNull { try { InfoCategory.valueOf(it) } catch (_: Exception) { null } }.toSet()
    }

    // --- متدهای جدید برای کنترل قابلیت جابجایی ---
    /**
     * وضعیت قابلیت جابجایی داشبورد را ذخیره می‌کند.
     */
    fun setReorderingEnabled(enabled: Boolean) {
        prefs.edit { putBoolean(KEY_DASHBOARD_REORDER_ENABLED, enabled) }
    }

    /**
     * وضعیت ذخیره شده قابلیت جابجایی را بازخوانی می‌کند.
     * به صورت پیش‌فرض، این قابلیت فعال است.
     */
    fun isReorderingEnabled(): Boolean {
        return prefs.getBoolean(KEY_DASHBOARD_REORDER_ENABLED, true)
    }



    // --- متدهای جدید برای مدیریت تم‌های رنگی ---

    /**
     * ذخیره تم رنگی از پیش تعریف شده
     */
    fun savePredefinedColorTheme(colorTheme: PredefinedColorTheme) {
        prefs.edit {
            putString(KEY_COLOR_THEME_TYPE, "predefined")
            putString(KEY_PREDEFINED_COLOR_THEME, colorTheme.id)
            remove(KEY_CUSTOM_PRIMARY_COLOR)
            remove(KEY_CUSTOM_SECONDARY_COLOR)
        }
    }

    /**
     * ذخیره تم رنگی سفارشی
     */
    fun saveCustomColorTheme(customTheme: CustomColorTheme) {
        prefs.edit {
            putString(KEY_COLOR_THEME_TYPE, "custom")
            putInt(KEY_CUSTOM_PRIMARY_COLOR, customTheme.primaryColor.toArgb())
            customTheme.secondaryColor?.let {
                putInt(KEY_CUSTOM_SECONDARY_COLOR, it.toArgb())
            } ?: remove(KEY_CUSTOM_SECONDARY_COLOR)
            remove(KEY_PREDEFINED_COLOR_THEME)
        }
    }

    /**
     * دریافت تم رنگی فعلی
     */
    fun getCurrentColorTheme(): ColorTheme? {
        val themeType = prefs.getString(KEY_COLOR_THEME_TYPE, null)

        return when (themeType) {
            "predefined" -> {
                val themeId = prefs.getString(KEY_PREDEFINED_COLOR_THEME, null)
                themeId?.let { PredefinedColorTheme.Companion.fromId(it)?.toColorTheme() }
            }
            "custom" -> {
                val primaryColorInt = prefs.getInt(KEY_CUSTOM_PRIMARY_COLOR, -1)
                if (primaryColorInt != -1) {
                    val primaryColor = Color(primaryColorInt)
                    val secondaryColorInt = prefs.getInt(KEY_CUSTOM_SECONDARY_COLOR, -1)
                    val secondaryColor = if (secondaryColorInt != -1) Color(secondaryColorInt) else null
                    CustomColorTheme(primaryColor, secondaryColor).toColorTheme()
                } else null
            }
            else -> null // هیچ تم رنگی انتخاب نشده
        }
    }

    /**
     * بازنشانی تم رنگی به حالت پیش‌فرض
     */
    fun resetColorTheme() {
        prefs.edit {
            remove(KEY_COLOR_THEME_TYPE)
            remove(KEY_PREDEFINED_COLOR_THEME)
            remove(KEY_CUSTOM_PRIMARY_COLOR)
            remove(KEY_CUSTOM_SECONDARY_COLOR)
        }
    }

    /**
     * بررسی اینکه آیا تم رنگی سفارشی انتخاب شده یا خیر
     */
    fun hasCustomColorTheme(): Boolean {
        return prefs.getString(KEY_COLOR_THEME_TYPE, null) != null
    }

    // بهینه‌سازی ۴: متدهای مدیریت کش و پاکسازی
    // Optimization 4: Cache management and cleanup methods

    /**
     * بهینه‌سازی ۴: محاسبه اندازه کل کش (تقریبی)
     * Optimization 4: Calculate total cache size (approximate)
     */
    fun getCacheSizeBytes(): Long {
        var totalSize = 0L

        // محاسبه اندازه کش اطلاعات دستگاه
        // Calculate device info cache size
        prefs.getString(KEY_DEVICE_INFO_CACHE, null)?.let {
            totalSize += it.toByteArray().size
        }

        // محاسبه اندازه کش برنامه‌ها
        // Calculate apps cache size
        prefs.getString(KEY_USER_APPS_CACHE, null)?.let {
            totalSize += it.toByteArray().size
        }
        prefs.getString(KEY_SYSTEM_APPS_CACHE, null)?.let {
            totalSize += it.toByteArray().size
        }

        // محاسبه اندازه تاریخچه تست‌ها
        // Calculate test history cache size
        prefs.getString(KEY_HEALTH_CHECK_HISTORY, null)?.let {
            totalSize += it.toByteArray().size
        }
        prefs.getString(KEY_PERFORMANCE_SCORE_HISTORY, null)?.let {
            totalSize += it.toByteArray().size
        }

        return totalSize
    }

    /**
     * بهینه‌سازی ۴: پاکسازی کش‌های منقضی شده
     * Optimization 4: Clean up expired caches
     */
    fun cleanupExpiredCaches() {
        val currentTime = System.currentTimeMillis()

        // پاکسازی کش اطلاعات دستگاه در صورت انقضا
        // Clean device info cache if expired
        if (!isDeviceInfoCacheValid()) {
            clearDeviceInfoCache()
        }

        // پاکسازی کش برنامه‌ها در صورت انقضا
        // Clean apps cache if expired
        if (!isAppsCacheValid()) {
            clearAppsCache()
        }

        // پاکسازی کش‌های قدیمی نسخه
        // Clean old version caches
        val cacheVersion = prefs.getInt(KEY_CACHE_VERSION, 0)
        if (cacheVersion < CURRENT_CACHE_VERSION) {
            clearAllCaches()
        }
    }

    /**
     * بهینه‌سازی ۴: پاکسازی تمام کش‌ها
     * Optimization 4: Clear all caches
     */
    fun clearAllCaches() {
        prefs.edit {
            // پاکسازی کش اطلاعات دستگاه
            // Clear device info cache
            remove(KEY_DEVICE_INFO_CACHE)
            remove(KEY_DEVICE_INFO_CACHE_TIMESTAMP)

            // پاکسازی کش برنامه‌ها
            // Clear apps cache
            remove(KEY_USER_APPS_CACHE)
            remove(KEY_SYSTEM_APPS_CACHE)
            remove(KEY_PACKAGE_COUNT)
            remove(KEY_USER_APPS_CACHE_TIMESTAMP)
            remove(KEY_SYSTEM_APPS_CACHE_TIMESTAMP)

            // به‌روزرسانی نسخه کش
            // Update cache version
            putInt(KEY_CACHE_VERSION, CURRENT_CACHE_VERSION)
        }
    }

    /**
     * بهینه‌سازی ۴: بررسی نیاز به پاکسازی کش بر اساس اندازه
     * Optimization 4: Check if cache cleanup is needed based on size
     */
    fun isCacheCleanupNeeded(): Boolean {
        val maxCacheSize = 10 * 1024 * 1024L // 10 MB حداکثر اندازه کش
        return getCacheSizeBytes() > maxCacheSize
    }

    /**
     * بهینه‌سازی ۴: پاکسازی هوشمند کش (قدیمی‌ترین ابتدا)
     * Optimization 4: Smart cache cleanup (oldest first)
     */
    fun performSmartCacheCleanup() {
        if (!isCacheCleanupNeeded()) return

        val currentTime = System.currentTimeMillis()
        val deviceInfoCacheTime = prefs.getLong(KEY_DEVICE_INFO_CACHE_TIMESTAMP, 0)
        val appsCacheTime = prefs.getLong(KEY_USER_APPS_CACHE_TIMESTAMP, 0)

        // پاکسازی قدیمی‌ترین کش ابتدا
        // Clean oldest cache first
        if (deviceInfoCacheTime < appsCacheTime) {
            clearDeviceInfoCache()
        } else {
            clearAppsCache()
        }
    }
}