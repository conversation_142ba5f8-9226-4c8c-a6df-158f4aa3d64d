package ir.dekot.kavosh.feature_testing.model

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * کلاس محاسبه معیارهای عملکرد CPU
 * Performance metrics calculation class for CPU
 */
@Singleton
class PerformanceMetrics @Inject constructor(
    private val cpuMonitor: CpuMonitor
) {
    
    /**
     * محاسبه امتیاز عملکرد کلی CPU
     * Calculate overall CPU performance score
     */
    suspend fun calculatePerformanceScore(
        cpuUsage: Float,
        temperature: Double,
        frequencies: List<Long>,
        maxFrequencies: List<Long>,
        testDuration: Long
    ): Int = withContext(Dispatchers.Default) {
        try {
            var score = 100
            
            // کاهش امتیاز بر اساس دما (۰-۳۰ امتیاز کاهش)
            score -= calculateTemperaturePenalty(temperature)
            
            // کاهش امتیاز بر اساس throttling (۰-۲۰ امتیاز کاهش)
            score -= calculateThrottlingPenalty(frequencies, maxFrequencies)
            
            // افزایش امتیاز بر اساس استفاده مؤثر (۰-۱۰ امتیاز افزایش)
            score += calculateUsageBonus(cpuUsage)
            
            // کاهش امتیاز بر اساس ناپایداری (۰-۱۵ امتیاز کاهش)
            score -= calculateStabilityPenalty(frequencies)
            
            // اعمال ضریب مدت زمان تست
            score = applyDurationFactor(score, testDuration)
            
            score.coerceIn(0, 100)
        } catch (e: Exception) {
            50 // امتیاز پیش‌فرض در صورت خطا
        }
    }
    
    /**
     * محاسبه امتیاز پایداری CPU
     * Calculate CPU stability score
     */
    suspend fun calculateStabilityScore(
        frequencyHistory: List<List<Long>>,
        temperatureHistory: List<Double>,
        usageHistory: List<Float>
    ): Int = withContext(Dispatchers.Default) {
        try {
            var stabilityScore = 100
            
            // بررسی پایداری فرکانس
            stabilityScore -= calculateFrequencyVariation(frequencyHistory)
            
            // بررسی پایداری دما
            stabilityScore -= calculateTemperatureVariation(temperatureHistory)
            
            // بررسی پایداری استفاده
            stabilityScore -= calculateUsageVariation(usageHistory)
            
            stabilityScore.coerceIn(0, 100)
        } catch (e: Exception) {
            50
        }
    }
    
    /**
     * محاسبه میانگین، حداکثر و حداقل فرکانس
     * Calculate average, maximum and minimum frequency
     */
    fun calculateFrequencyStats(frequencies: List<Long>): FrequencyStats {
        return if (frequencies.isEmpty()) {
            FrequencyStats(0.0, 0L, 0L)
        } else {
            FrequencyStats(
                average = frequencies.average(),
                maximum = frequencies.maxOrNull() ?: 0L,
                minimum = frequencies.minOrNull() ?: 0L
            )
        }
    }
    
    /**
     * محاسبه آمار دما
     * Calculate temperature statistics
     */
    fun calculateTemperatureStats(temperatures: List<Double>): TemperatureStats {
        return if (temperatures.isEmpty()) {
            TemperatureStats(0.0, 0.0, 0.0)
        } else {
            TemperatureStats(
                average = temperatures.average(),
                maximum = temperatures.maxOrNull() ?: 0.0,
                minimum = temperatures.minOrNull() ?: 0.0
            )
        }
    }
    
    /**
     * محاسبه آمار استفاده CPU
     * Calculate CPU usage statistics
     */
    fun calculateUsageStats(usages: List<Float>): UsageStats {
        return if (usages.isEmpty()) {
            UsageStats(0f, 0f, 0f)
        } else {
            UsageStats(
                average = usages.average().toFloat(),
                maximum = usages.maxOrNull() ?: 0f,
                minimum = usages.minOrNull() ?: 0f
            )
        }
    }
    
    /**
     * تشخیص الگوهای عملکرد
     * Detect performance patterns
     */
    suspend fun detectPerformancePatterns(
        frequencyHistory: List<List<Long>>,
        temperatureHistory: List<Double>,
        usageHistory: List<Float>
    ): List<PerformancePattern> = withContext(Dispatchers.Default) {
        val patterns = mutableListOf<PerformancePattern>()
        
        // تشخیص throttling
        if (detectThrottlingPattern(frequencyHistory, temperatureHistory)) {
            patterns.add(PerformancePattern.THERMAL_THROTTLING)
        }
        
        // تشخیص عملکرد پایدار
        if (detectStablePerformance(frequencyHistory, usageHistory)) {
            patterns.add(PerformancePattern.STABLE_PERFORMANCE)
        }
        
        // تشخیص نوسانات شدید
        if (detectHighVariation(frequencyHistory)) {
            patterns.add(PerformancePattern.HIGH_VARIATION)
        }
        
        // تشخیص عملکرد بهینه
        if (detectOptimalPerformance(frequencyHistory, temperatureHistory, usageHistory)) {
            patterns.add(PerformancePattern.OPTIMAL_PERFORMANCE)
        }
        
        patterns
    }
    
    // توابع کمکی خصوصی
    private fun calculateTemperaturePenalty(temperature: Double): Int {
        return when {
            temperature <= 0 -> 0 // دما خوانده نشده
            temperature < 40 -> 0 // دمای عادی
            temperature < 60 -> 5 // دمای متوسط
            temperature < 80 -> 15 // دمای بالا
            temperature < 90 -> 25 // دمای خطرناک
            else -> 30 // دمای بحرانی
        }
    }
    
    private fun calculateThrottlingPenalty(frequencies: List<Long>, maxFrequencies: List<Long>): Int {
        if (frequencies.isEmpty() || maxFrequencies.isEmpty()) return 0
        
        val avgFreqRatio = frequencies.zip(maxFrequencies) { current, max ->
            if (max > 0) current.toDouble() / max else 1.0
        }.average()
        
        return when {
            avgFreqRatio > 0.9 -> 0 // عملکرد کامل
            avgFreqRatio > 0.8 -> 5 // throttling خفیف
            avgFreqRatio > 0.7 -> 10 // throttling متوسط
            avgFreqRatio > 0.5 -> 15 // throttling شدید
            else -> 20 // throttling بحرانی
        }
    }
    
    private fun calculateUsageBonus(cpuUsage: Float): Int {
        return when {
            cpuUsage > 90 -> 10 // استفاده کامل
            cpuUsage > 80 -> 8 // استفاده بالا
            cpuUsage > 70 -> 5 // استفاده خوب
            cpuUsage > 50 -> 3 // استفاده متوسط
            else -> 0 // استفاده کم
        }
    }
    
    private fun calculateStabilityPenalty(frequencies: List<Long>): Int {
        if (frequencies.size < 2) return 0
        
        val mean = frequencies.average()
        val variance = frequencies.map { (it - mean).pow(2) }.average()
        val stdDev = sqrt(variance)
        val coefficientOfVariation = if (mean > 0) stdDev / mean else 0.0
        
        return when {
            coefficientOfVariation < 0.05 -> 0 // بسیار پایدار
            coefficientOfVariation < 0.1 -> 3 // پایدار
            coefficientOfVariation < 0.2 -> 8 // متوسط
            coefficientOfVariation < 0.3 -> 12 // ناپایدار
            else -> 15 // بسیار ناپایدار
        }
    }
    
    private fun applyDurationFactor(score: Int, testDuration: Long): Int {
        val durationSeconds = testDuration / 1000
        return when {
            durationSeconds < 10 -> (score * 0.8).toInt() // تست کوتاه
            durationSeconds < 30 -> (score * 0.9).toInt() // تست متوسط
            durationSeconds < 60 -> score // تست استاندارد
            else -> (score * 1.1).toInt().coerceAtMost(100) // تست طولانی
        }
    }
    
    private fun calculateFrequencyVariation(frequencyHistory: List<List<Long>>): Int {
        if (frequencyHistory.size < 3) return 0
        
        val avgFrequencies = frequencyHistory.map { it.average() }
        val mean = avgFrequencies.average()
        val variance = avgFrequencies.map { (it - mean).pow(2) }.average()
        val coefficientOfVariation = if (mean > 0) sqrt(variance) / mean else 0.0
        
        return (coefficientOfVariation * 20).toInt().coerceAtMost(15)
    }
    
    private fun calculateTemperatureVariation(temperatureHistory: List<Double>): Int {
        if (temperatureHistory.size < 3) return 0
        
        val mean = temperatureHistory.average()
        val variance = temperatureHistory.map { (it - mean).pow(2) }.average()
        val coefficientOfVariation = if (mean > 0) sqrt(variance) / mean else 0.0
        
        return (coefficientOfVariation * 30).toInt().coerceAtMost(10)
    }
    
    private fun calculateUsageVariation(usageHistory: List<Float>): Int {
        if (usageHistory.size < 3) return 0
        
        val mean = usageHistory.average()
        val variance = usageHistory.map { (it - mean).pow(2) }.average()
        val coefficientOfVariation = if (mean > 0) sqrt(variance) / mean else 0.0
        
        return (coefficientOfVariation * 25).toInt().coerceAtMost(10)
    }
    
    private fun detectThrottlingPattern(
        frequencyHistory: List<List<Long>>,
        temperatureHistory: List<Double>
    ): Boolean {
        if (frequencyHistory.size < 5 || temperatureHistory.size < 5) return false
        
        val recentFreqs = frequencyHistory.takeLast(5).map { it.average() }
        val recentTemps = temperatureHistory.takeLast(5)
        
        val freqTrend = recentFreqs.last() - recentFreqs.first()
        val tempTrend = recentTemps.last() - recentTemps.first()
        
        return freqTrend < -100 && tempTrend > 5 // فرکانس کاهش و دما افزایش
    }
    
    private fun detectStablePerformance(
        frequencyHistory: List<List<Long>>,
        usageHistory: List<Float>
    ): Boolean {
        if (frequencyHistory.size < 10) return false
        
        val avgFrequencies = frequencyHistory.map { it.average() }
        val freqVariation = calculateVariationCoefficient(avgFrequencies)
        val usageVariation = calculateVariationCoefficient(usageHistory.map { it.toDouble() })
        
        return freqVariation < 0.1 && usageVariation < 0.2
    }
    
    private fun detectHighVariation(frequencyHistory: List<List<Long>>): Boolean {
        if (frequencyHistory.size < 5) return false
        
        val avgFrequencies = frequencyHistory.map { it.average() }
        val variation = calculateVariationCoefficient(avgFrequencies)
        
        return variation > 0.3
    }
    
    private fun detectOptimalPerformance(
        frequencyHistory: List<List<Long>>,
        temperatureHistory: List<Double>,
        usageHistory: List<Float>
    ): Boolean {
        if (frequencyHistory.isEmpty() || temperatureHistory.isEmpty() || usageHistory.isEmpty()) return false
        
        val avgTemp = temperatureHistory.average()
        val avgUsage = usageHistory.average()
        val freqVariation = calculateVariationCoefficient(frequencyHistory.map { it.average() })
        
        return avgTemp < 70 && avgUsage > 80 && freqVariation < 0.15
    }
    
    private fun calculateVariationCoefficient(values: List<Double>): Double {
        if (values.size < 2) return 0.0
        
        val mean = values.average()
        val variance = values.map { (it - mean).pow(2) }.average()
        
        return if (mean > 0) sqrt(variance) / mean else 0.0
    }
}

/**
 * آمار فرکانس
 */
data class FrequencyStats(
    val average: Double,
    val maximum: Long,
    val minimum: Long
)

/**
 * آمار دما
 */
data class TemperatureStats(
    val average: Double,
    val maximum: Double,
    val minimum: Double
)

/**
 * آمار استفاده
 */
data class UsageStats(
    val average: Float,
    val maximum: Float,
    val minimum: Float
)

/**
 * الگوهای عملکرد
 */
enum class PerformancePattern {
    THERMAL_THROTTLING,    // کاهش عملکرد حرارتی
    STABLE_PERFORMANCE,    // عملکرد پایدار
    HIGH_VARIATION,        // نوسانات بالا
    OPTIMAL_PERFORMANCE    // عملکرد بهینه
}
