package ir.dekot.kavosh.feature_testing.model

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.roundToInt

/**
 * کلاس جمع‌آوری داده‌های زنده CPU
 * Real-time CPU data collector class
 */
@Singleton
class RealTimeDataCollector @Inject constructor(
    private val cpuMonitor: CpuMonitor,
    private val performanceMetrics: PerformanceMetrics
) {
    
    private var collectionJob: Job? = null
    private val collectionScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // StateFlow برای داده‌های زنده
    private val _realTimeData = MutableStateFlow(RealTimeData())
    val realTimeData: StateFlow<RealTimeData> = _realTimeData.asStateFlow()
    
    // تاریخچه داده‌ها برای محاسبات آماری
    private val frequencyHistory = mutableListOf<List<Long>>()
    private val temperatureHistory = mutableListOf<Double>()
    private val usageHistory = mutableListOf<Float>()
    private val powerHistory = mutableListOf<Double>()
    
    // تنظیمات جمع‌آوری
    private var samplingInterval = 1000L // میلی‌ثانیه
    private var maxHistorySize = 300 // حداکثر ۵ دقیقه با نمونه‌برداری هر ثانیه
    
    /**
     * شروع جمع‌آوری داده‌های زنده
     * Start real-time data collection
     */
    fun startCollection(intervalMs: Long = 1000L) {
        stopCollection()
        
        samplingInterval = intervalMs
        clearHistory()
        
        collectionJob = collectionScope.launch {
            while (isActive) {
                try {
                    collectDataPoint()
                    delay(samplingInterval)
                } catch (e: CancellationException) {
                    break
                } catch (e: Exception) {
                    // ادامه جمع‌آوری در صورت خطا
                    delay(samplingInterval)
                }
            }
        }
    }
    
    /**
     * توقف جمع‌آوری داده‌های زنده
     * Stop real-time data collection
     */
    fun stopCollection() {
        collectionJob?.cancel()
        collectionJob = null
    }
    
    /**
     * پاکسازی تاریخچه داده‌ها
     * Clear data history
     */
    fun clearHistory() {
        frequencyHistory.clear()
        temperatureHistory.clear()
        usageHistory.clear()
        powerHistory.clear()
    }
    
    /**
     * تنظیم تعداد هسته‌های فعال برای مانیتورینگ
     * Set active core count for monitoring
     */
    fun setActiveCoreCount(coreCount: Int) {
        cpuMonitor.setActiveCoreCount(coreCount)
    }
    
    /**
     * دریافت آمار کلی عملکرد
     * Get overall performance statistics
     */
    suspend fun getPerformanceStatistics(): PerformanceStatistics {
        return withContext(Dispatchers.Default) {
            val frequencyStats = if (frequencyHistory.isNotEmpty()) {
                val allFrequencies = frequencyHistory.flatten()
                performanceMetrics.calculateFrequencyStats(allFrequencies)
            } else {
                FrequencyStats(0.0, 0L, 0L)
            }
            
            val temperatureStats = performanceMetrics.calculateTemperatureStats(temperatureHistory)
            val usageStats = performanceMetrics.calculateUsageStats(usageHistory)
            
            val performanceScore = if (frequencyHistory.isNotEmpty() && temperatureHistory.isNotEmpty() && usageHistory.isNotEmpty()) {
                val currentFreqs = frequencyHistory.lastOrNull() ?: emptyList()
                val maxFreqs = cpuMonitor.getMaxCoreFrequencies()
                val currentTemp = temperatureHistory.lastOrNull() ?: 0.0
                val currentUsage = usageHistory.lastOrNull() ?: 0f
                val testDuration = frequencyHistory.size * samplingInterval
                
                performanceMetrics.calculatePerformanceScore(
                    currentUsage, currentTemp, currentFreqs, maxFreqs, testDuration
                )
            } else {
                0
            }
            
            val stabilityScore = if (frequencyHistory.size > 5) {
                performanceMetrics.calculateStabilityScore(
                    frequencyHistory, temperatureHistory, usageHistory
                )
            } else {
                0
            }
            
            val patterns = if (frequencyHistory.size > 10) {
                performanceMetrics.detectPerformancePatterns(
                    frequencyHistory, temperatureHistory, usageHistory
                )
            } else {
                emptyList()
            }
            
            PerformanceStatistics(
                frequencyStats = frequencyStats,
                temperatureStats = temperatureStats,
                usageStats = usageStats,
                performanceScore = performanceScore,
                stabilityScore = stabilityScore,
                patterns = patterns,
                averagePowerConsumption = if (powerHistory.isNotEmpty()) powerHistory.average() else 0.0,
                testDuration = frequencyHistory.size * samplingInterval,
                dataPoints = frequencyHistory.size
            )
        }
    }
    
    /**
     * دریافت تاریخچه داده‌ها برای نمودارها
     * Get data history for charts
     */
    fun getChartData(): ChartData {
        return ChartData(
            frequencyHistory = frequencyHistory.toList(),
            temperatureHistory = temperatureHistory.toList(),
            usageHistory = usageHistory.toList(),
            powerHistory = powerHistory.toList(),
            timestamps = generateTimestamps()
        )
    }
    
    /**
     * تنظیم فاصله نمونه‌برداری
     * Set sampling interval
     */
    fun setSamplingInterval(intervalMs: Long) {
        samplingInterval = intervalMs.coerceIn(500L, 5000L)
    }
    
    /**
     * تنظیم حداکثر اندازه تاریخچه
     * Set maximum history size
     */
    fun setMaxHistorySize(size: Int) {
        maxHistorySize = size.coerceIn(50, 1000)
    }
    
    // توابع خصوصی
    private suspend fun collectDataPoint() {
        val cpuUsage = cpuMonitor.getCpuUsagePercentage()
        val temperature = cpuMonitor.getCpuTemperature()
        val frequencies = cpuMonitor.getCoreFrequencies()
        val maxFrequencies = cpuMonitor.getMaxCoreFrequencies()
        val powerConsumption = cpuMonitor.getEstimatedPowerConsumption()
        val isThrottling = cpuMonitor.isThrottling()
        val perCoreUsage = cpuMonitor.getPerCoreUsage()
        
        // محاسبه امتیاز عملکرد فوری
        val instantPerformanceScore = if (frequencies.isNotEmpty() && maxFrequencies.isNotEmpty()) {
            performanceMetrics.calculatePerformanceScore(
                cpuUsage, temperature, frequencies, maxFrequencies, samplingInterval
            )
        } else {
            0
        }
        
        // به‌روزرسانی داده‌های زنده
        val newData = RealTimeData(
            cpuUsage = cpuUsage,
            temperature = temperature,
            frequencies = frequencies,
            maxFrequencies = maxFrequencies,
            powerConsumption = powerConsumption,
            isThrottling = isThrottling,
            perCoreUsage = perCoreUsage,
            performanceScore = instantPerformanceScore,
            timestamp = System.currentTimeMillis()
        )
        
        _realTimeData.value = newData
        
        // اضافه کردن به تاریخچه
        addToHistory(frequencies, temperature, cpuUsage, powerConsumption)
    }
    
    private fun addToHistory(
        frequencies: List<Long>,
        temperature: Double,
        cpuUsage: Float,
        powerConsumption: Double
    ) {
        // اضافه کردن داده‌های جدید
        frequencyHistory.add(frequencies)
        temperatureHistory.add(temperature)
        usageHistory.add(cpuUsage)
        powerHistory.add(powerConsumption)
        
        // حذف داده‌های قدیمی اگر از حد مجاز بیشتر شد
        if (frequencyHistory.size > maxHistorySize) {
            frequencyHistory.removeAt(0)
            temperatureHistory.removeAt(0)
            usageHistory.removeAt(0)
            powerHistory.removeAt(0)
        }
    }
    
    private fun generateTimestamps(): List<Long> {
        val currentTime = System.currentTimeMillis()
        return (0 until frequencyHistory.size).map { index ->
            currentTime - (frequencyHistory.size - 1 - index) * samplingInterval
        }
    }
    
    /**
     * پاکسازی منابع
     * Clean up resources
     */
    fun cleanup() {
        stopCollection()
        collectionScope.cancel()
        clearHistory()
    }
}

/**
 * داده‌های زنده CPU
 * Real-time CPU data
 */
data class RealTimeData(
    val cpuUsage: Float = 0f,
    val temperature: Double = 0.0,
    val frequencies: List<Long> = emptyList(),
    val maxFrequencies: List<Long> = emptyList(),
    val powerConsumption: Double = 0.0,
    val isThrottling: Boolean = false,
    val perCoreUsage: List<Float> = emptyList(),
    val performanceScore: Int = 0,
    val timestamp: Long = System.currentTimeMillis()
) {
    /**
     * درصد فرکانس نسبت به حداکثر
     * Frequency percentage relative to maximum
     */
    val frequencyPercentages: List<Float>
        get() = frequencies.zip(maxFrequencies) { current, max ->
            if (max > 0) (current.toFloat() / max * 100f).roundToInt().toFloat()
            else 0f
        }
    
    /**
     * میانگین فرکانس
     * Average frequency
     */
    val averageFrequency: Double
        get() = if (frequencies.isNotEmpty()) frequencies.average() else 0.0
    
    /**
     * وضعیت حرارتی
     * Thermal status
     */
    val thermalStatus: ThermalStatus
        get() = when {
            temperature <= 0 -> ThermalStatus.UNKNOWN
            temperature < 50 -> ThermalStatus.NORMAL
            temperature < 70 -> ThermalStatus.WARM
            temperature < 85 -> ThermalStatus.HOT
            else -> ThermalStatus.CRITICAL
        }
}

/**
 * آمار عملکرد کلی
 * Overall performance statistics
 */
data class PerformanceStatistics(
    val frequencyStats: FrequencyStats,
    val temperatureStats: TemperatureStats,
    val usageStats: UsageStats,
    val performanceScore: Int,
    val stabilityScore: Int,
    val patterns: List<PerformancePattern>,
    val averagePowerConsumption: Double,
    val testDuration: Long,
    val dataPoints: Int
)

/**
 * داده‌های نمودار
 * Chart data
 */
data class ChartData(
    val frequencyHistory: List<List<Long>>,
    val temperatureHistory: List<Double>,
    val usageHistory: List<Float>,
    val powerHistory: List<Double>,
    val timestamps: List<Long>
)

/**
 * وضعیت حرارتی
 * Thermal status
 */
enum class ThermalStatus {
    UNKNOWN,    // نامشخص
    NORMAL,     // عادی
    WARM,       // گرم
    HOT,        // داغ
    CRITICAL    // بحرانی
}
