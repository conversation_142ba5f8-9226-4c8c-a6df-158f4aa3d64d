package ir.dekot.kavosh.feature_testing.model

import android.content.Context
import android.os.Build
import dagger.hilt.android.qualifiers.ApplicationContext
import ir.dekot.kavosh.R
import ir.dekot.kavosh.feature_deviceInfo.model.MemoryDataSource
import ir.dekot.kavosh.feature_deviceInfo.model.PowerDataSource
import ir.dekot.kavosh.feature_deviceInfo.model.SocDataSource
import ir.dekot.kavosh.feature_deviceInfo.model.SystemDataSource
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.collections.plus
import kotlin.compareTo
import kotlin.random.Random
import kotlin.text.toDouble
import kotlin.text.toInt

/**
 * منبع داده برای ابزارهای تشخیصی
 * شامل Health Check و Performance Score
 */
@Singleton
class DiagnosticDataSource @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val socDataSource: SocDataSource,
    private val memoryDataSource: MemoryDataSource,
    private val powerDataSource: PowerDataSource,
    private val systemDataSource: SystemDataSource
) {

    /**
     * انجام بررسی سلامت کلی دستگاه
     */
    suspend fun performHealthCheck(): HealthCheckResult {
        val checks = mutableListOf<HealthCheck>()

        // بررسی عملکرد
        checks.add(checkPerformance())

        // بررسی حافظه
        checks.add(checkStorage())

        // بررسی باتری
        checks.add(checkBattery())

        // بررسی دما
        checks.add(checkTemperature())

        // بررسی رم
        checks.add(checkMemory())

        // بررسی شبکه
        checks.add(checkNetwork())

        // بررسی امنیت
        checks.add(checkSecurity())

        // بررسی سیستم
        checks.add(checkSystem())

        // محاسبه امتیاز کلی
        val overallScore = checks.map { it.score }.average().toInt()
        val overallStatus = getHealthStatus(overallScore)

        // تولید توصیه‌ها
        val recommendations = generateRecommendations(checks)

        return HealthCheckResult(
            overallScore = overallScore,
            overallStatus = overallStatus,
            checks = checks,
            recommendations = recommendations
        )
    }

    /**
     * محاسبه امتیاز عملکرد
     */
    suspend fun calculatePerformanceScore(): PerformanceScore {
        val categoryScores = mutableListOf<CategoryScore>()
        val benchmarkResults = mutableListOf<BenchmarkResult>()

        // تست CPU
        val cpuScore = performCpuBenchmark()
        categoryScores.add(
            CategoryScore(
                category = PerformanceCategory.CPU,
                score = cpuScore.score,
                grade = getPerformanceGrade(cpuScore.score),
                details = cpuScore.description,
                testResults = cpuScore.testResults
            )
        )
        benchmarkResults.addAll(cpuScore.benchmarkResults)

        // تست GPU
        val gpuScore = performGpuBenchmark()
        categoryScores.add(
            CategoryScore(
                category = PerformanceCategory.GPU,
                score = gpuScore.score,
                grade = getPerformanceGrade(gpuScore.score),
                details = gpuScore.description
            )
        )
        benchmarkResults.addAll(gpuScore.benchmarkResults)

        // تست RAM
        val ramScore = performRamBenchmark()
        categoryScores.add(
            CategoryScore(
                category = PerformanceCategory.RAM,
                score = ramScore.score,
                grade = getPerformanceGrade(ramScore.score),
                details = ramScore.description
            )
        )
        benchmarkResults.addAll(ramScore.benchmarkResults)

        // تست Storage
        val storageScore = performStorageBenchmark()
        categoryScores.add(
            CategoryScore(
                category = PerformanceCategory.STORAGE,
                score = storageScore.score,
                grade = getPerformanceGrade(storageScore.score),
                details = storageScore.description
            )
        )
        benchmarkResults.addAll(storageScore.benchmarkResults)

        // محاسبه امتیاز کلی
        val overallScore = categoryScores.map { it.score }.average().toInt()
        val performanceGrade = getPerformanceGrade(overallScore)

        // رتبه‌بندی دستگاه (شبیه‌سازی)
        val deviceRanking = generateDeviceRanking(overallScore)

        return PerformanceScore(
            overallScore = overallScore,
            performanceGrade = performanceGrade,
            categoryScores = categoryScores,
            benchmarkResults = benchmarkResults,
            deviceRanking = deviceRanking
        )
    }



    // متدهای کمکی برای Health Check
    private suspend fun checkPerformance(): HealthCheck {
        delay(500) // شبیه‌سازی تست
        val score = Random.Default.nextInt(70, 95)
        return HealthCheck(
            category = HealthCategory.PERFORMANCE,
            name = context.getString(R.string.health_check_performance),
            score = score,
            status = getHealthStatus(score),
            description = context.getString(R.string.health_check_performance_desc),
            recommendation = if (score < 80) context.getString(R.string.health_check_performance_rec) else null
        )
    }

    private suspend fun checkStorage(): HealthCheck {
        delay(300)
        val storageInfo = memoryDataSource.getStorageInfo()
        // شبیه‌سازی محاسبه فضای خالی - در واقعیت باید از StorageInfo استفاده کرد
        val freeSpacePercent = Random.Default.nextInt(20, 80) // شبیه‌سازی
        val score = when {
            freeSpacePercent > 50 -> Random.Default.nextInt(85, 100)
            freeSpacePercent > 20 -> Random.Default.nextInt(60, 84)
            else -> Random.Default.nextInt(30, 59)
        }

        return HealthCheck(
            category = HealthCategory.STORAGE,
            name = context.getString(R.string.health_check_storage),
            score = score,
            status = getHealthStatus(score),
            description = context.getString(R.string.health_check_storage_desc, freeSpacePercent),
            recommendation = if (score < 70) context.getString(R.string.health_check_storage_rec) else null
        )
    }

    private suspend fun checkBattery(): HealthCheck {
        delay(200)
        // شبیه‌سازی وضعیت باتری - در واقعیت باید Intent را از جای دیگر دریافت کرد
        val batteryHealth = listOf("Good", "Fair", "Poor").random()
        val score = when (batteryHealth) {
            "Good" -> Random.Default.nextInt(80, 100)
            "Fair" -> Random.Default.nextInt(60, 79)
            else -> Random.Default.nextInt(30, 59)
        }

        return HealthCheck(
            category = HealthCategory.BATTERY,
            name = context.getString(R.string.health_check_battery),
            score = score,
            status = getHealthStatus(score),
            description = context.getString(R.string.health_check_battery_desc, batteryHealth),
            recommendation = if (score < 70) context.getString(R.string.health_check_battery_rec) else null
        )
    }

    private suspend fun checkTemperature(): HealthCheck {
        delay(100)
        val score = Random.Default.nextInt(75, 95) // شبیه‌سازی - در واقعیت باید دمای واقعی خوانده شود
        return HealthCheck(
            category = HealthCategory.TEMPERATURE,
            name = context.getString(R.string.health_check_temperature),
            score = score,
            status = getHealthStatus(score),
            description = context.getString(R.string.health_check_temperature_desc),
            recommendation = if (score < 70) context.getString(R.string.health_check_temperature_rec) else null
        )
    }

    private suspend fun checkMemory(): HealthCheck {
        delay(200)
        val ramInfo = memoryDataSource.getRamInfo()
        // شبیه‌سازی محاسبه درصد رم آزاد
        val freeRamPercent = Random.Default.nextInt(20, 60) // شبیه‌سازی
        val score = when {
            freeRamPercent > 40 -> Random.Default.nextInt(85, 100)
            freeRamPercent > 20 -> Random.Default.nextInt(65, 84)
            else -> Random.Default.nextInt(40, 64)
        }

        return HealthCheck(
            category = HealthCategory.MEMORY,
            name = context.getString(R.string.health_check_memory),
            score = score,
            status = getHealthStatus(score),
            description = context.getString(R.string.health_check_memory_desc, freeRamPercent),
            recommendation = if (score < 70) context.getString(R.string.health_check_memory_rec) else null
        )
    }

    private suspend fun checkNetwork(): HealthCheck {
        delay(300)
        val score = Random.Default.nextInt(80, 95)
        return HealthCheck(
            category = HealthCategory.NETWORK,
            name = context.getString(R.string.health_check_network),
            score = score,
            status = getHealthStatus(score),
            description = context.getString(R.string.health_check_network_desc)
        )
    }

    private suspend fun checkSecurity(): HealthCheck {
        delay(400)
        val score =
            Random.Default.nextInt(85, 100)

        return HealthCheck(
            category = HealthCategory.SECURITY,
            name = context.getString(R.string.health_check_security),
            score = score,
            status = getHealthStatus(score),
            description = context.getString(R.string.health_check_security_desc),
            recommendation = if (score < 80) context.getString(R.string.health_check_security_rec) else null
        )
    }

    private suspend fun checkSystem(): HealthCheck {
        delay(200)
        val score = Random.Default.nextInt(80, 95)
        return HealthCheck(
            category = HealthCategory.SYSTEM,
            name = context.getString(R.string.health_check_system),
            score = score,
            status = getHealthStatus(score),
            description = context.getString(
                R.string.health_check_system_desc,
                Build.VERSION.RELEASE
            )
        )
    }

    private fun getHealthStatus(score: Int): HealthStatus = when (score) {
        in 90..100 -> HealthStatus.EXCELLENT
        in 70..89 -> HealthStatus.GOOD
        in 50..69 -> HealthStatus.FAIR
        in 30..49 -> HealthStatus.POOR
        else -> HealthStatus.CRITICAL
    }

    private fun generateRecommendations(checks: List<HealthCheck>): List<String> {
        return checks.filter { it.score< 80 }
            .mapNotNull { it.recommendation }
            .distinct()
    }

    // متدهای کمکی برای Performance Score
    private suspend fun performCpuBenchmark(): CategoryBenchmarkResult {
        delay(2000) // شبیه‌سازی تست CPU
        val score = Random.Default.nextInt(70, 95)
        val benchmarkResults = listOf(
            BenchmarkResult(
                testName = "Single Core",
                category = PerformanceCategory.CPU,
                score = Random.Default.nextInt(800, 1200),
                unit = "points",
                description = "Single-threaded performance",
                duration = 1000
            ),
            BenchmarkResult(
                testName = "Multi Core",
                category = PerformanceCategory.CPU,
                score = Random.Default.nextInt(2500, 4000),
                unit = "points",
                description = "Multi-threaded performance",
                duration = 1000
            )
        )

        return CategoryBenchmarkResult(
            score = score,
            description = "CPU performance based on computational tests",
            benchmarkResults = benchmarkResults,
            testResults = emptyList()
        )
    }

    private suspend fun performGpuBenchmark(): CategoryBenchmarkResult {
        delay(1500)
        val score = Random.Default.nextInt(65, 90)
        val benchmarkResults = listOf(
            BenchmarkResult(
                testName = "3D Graphics",
                category = PerformanceCategory.GPU,
                score = Random.Default.nextInt(15, 35),
                unit = "fps",
                description = "3D rendering performance",
                duration = 1500
            )
        )

        return CategoryBenchmarkResult(
            score = score,
            description = "GPU performance based on graphics rendering",
            benchmarkResults = benchmarkResults
        )
    }

    private suspend fun performRamBenchmark(): CategoryBenchmarkResult {
        delay(1000)
        val score = Random.Default.nextInt(75, 95)
        val benchmarkResults = listOf(
            BenchmarkResult(
                testName = "Memory Bandwidth",
                category = PerformanceCategory.RAM,
                score = Random.Default.nextInt(8000, 15000),
                unit = "MB/s",
                description = "Memory read/write speed",
                duration = 1000
            )
        )

        return CategoryBenchmarkResult(
            score = score,
            description = "RAM performance based on memory operations",
            benchmarkResults = benchmarkResults
        )
    }

    private suspend fun performStorageBenchmark(): CategoryBenchmarkResult {
        delay(2000)
        val score = Random.Default.nextInt(60, 85)
        val benchmarkResults = listOf(
            BenchmarkResult(
                testName = "Sequential Read",
                category = PerformanceCategory.STORAGE,
                score = Random.Default.nextInt(200, 800),
                unit = "MB/s",
                description = "Sequential read speed",
                duration = 1000
            ),
            BenchmarkResult(
                testName = "Sequential Write",
                category = PerformanceCategory.STORAGE,
                score = Random.Default.nextInt(100, 400),
                unit = "MB/s",
                description = "Sequential write speed",
                duration = 1000
            )
        )

        return CategoryBenchmarkResult(
            score = score,
            description = "Storage performance based on I/O operations",
            benchmarkResults = benchmarkResults
        )
    }

    private fun getPerformanceGrade(score: Int): PerformanceGrade = when (score) {
        in 95..100 -> PerformanceGrade.S_PLUS
        in 90..94 -> PerformanceGrade.S
        in 85..89 -> PerformanceGrade.A_PLUS
        in 80..84 -> PerformanceGrade.A
        in 75..79 -> PerformanceGrade.B_PLUS
        in 70..74 -> PerformanceGrade.B
        in 65..69 -> PerformanceGrade.C_PLUS
        in 60..64 -> PerformanceGrade.C
        in 50..59 -> PerformanceGrade.D
        else -> PerformanceGrade.F
    }

    private fun generateDeviceRanking(score: Int): DeviceRanking {
        val totalDevices = Random.Default.nextInt(50000, 100000)
        val percentile = score.toDouble()
        val globalRank = ((100 - percentile) / 100 * totalDevices).toInt()

        return DeviceRanking(
            globalRank = globalRank,
            totalDevices = totalDevices,
            percentile = percentile,
            similarDevices = generateSimilarDevices(score)
        )
    }

    private fun generateSimilarDevices(currentScore: Int): List<SimilarDevice> {
        return listOf(
            SimilarDevice(
                "Samsung Galaxy S23",
                currentScore + Random.Default.nextInt(-10, 10),
                Random.Default.nextInt(-10, 10)
            ),
            SimilarDevice(
                "iPhone 14",
                currentScore + Random.Default.nextInt(-15, 15),
                Random.Default.nextInt(-15, 15)
            ),
            SimilarDevice(
                "Google Pixel 7",
                currentScore + Random.Default.nextInt(-8, 8),
                Random.Default.nextInt(-8, 8)
            ),
            SimilarDevice(
                "OnePlus 11",
                currentScore + Random.Default.nextInt(-12, 12),
                Random.Default.nextInt(-12, 12)
            )
        )
    }



    // کلاس کمکی برای نتایج benchmark
    private data class CategoryBenchmarkResult(
        val score: Int,
        val description: String,
        val benchmarkResults: List<BenchmarkResult>,
        val testResults: List<TestResult> = emptyList()
    )
}