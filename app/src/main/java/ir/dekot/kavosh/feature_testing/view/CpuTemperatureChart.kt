package ir.dekot.kavosh.feature_testing.view

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.*

/**
 * نمودار زمان-واقعی دمای CPU
 * Real-time CPU temperature chart
 */
@Composable
fun CpuTemperatureChart(
    temperatureHistory: List<Double>,
    modifier: Modifier = Modifier,
    maxTemperature: Double = 100.0,
    warningTemperature: Double = 70.0,
    criticalTemperature: Double = 85.0,
    animationDuration: Int = 300
) {
    // انیمیشن برای نمودار
    val animatedProgress by animateFloatAsState(
        targetValue = if (temperatureHistory.isNotEmpty()) 1f else 0f,
        animationSpec = tween(animationDuration),
        label = "TempChartAnimation"
    )
    
    // رنگ‌های دما
    val normalColor = Color(0xFF4CAF50) // سبز
    val warningColor = Color(0xFFFF9800) // نارنجی
    val criticalColor = Color(0xFFF44336) // قرمز

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // عنوان و آمار فعلی
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "نمودار دمای پردازنده",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                if (temperatureHistory.isNotEmpty()) {
                    val currentTemp = temperatureHistory.lastOrNull() ?: 0.0
                    val tempColor = when {
                        currentTemp >= criticalTemperature -> criticalColor
                        currentTemp >= warningTemperature -> warningColor
                        else -> normalColor
                    }
                    
                    Text(
                        text = "${currentTemp.toInt()}°C",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = tempColor
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // نمودار اصلی
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(180.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f))
            ) {
                if (temperatureHistory.isNotEmpty()) {
                    Canvas(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        drawTemperatureChart(
                            temperatureHistory = temperatureHistory,
                            maxTemperature = maxTemperature,
                            warningTemperature = warningTemperature,
                            criticalTemperature = criticalTemperature,
                            normalColor = normalColor,
                            warningColor = warningColor,
                            criticalColor = criticalColor,
                            animatedProgress = animatedProgress
                        )
                    }
                } else {
                    // پیام خالی بودن داده
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "در انتظار داده دما...",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // راهنمای رنگ‌ها و آمار
            TemperatureStats(
                temperatureHistory = temperatureHistory,
                warningTemperature = warningTemperature,
                criticalTemperature = criticalTemperature,
                normalColor = normalColor,
                warningColor = warningColor,
                criticalColor = criticalColor
            )
        }
    }
}

/**
 * رسم نمودار دما
 */
private fun DrawScope.drawTemperatureChart(
    temperatureHistory: List<Double>,
    maxTemperature: Double,
    warningTemperature: Double,
    criticalTemperature: Double,
    normalColor: Color,
    warningColor: Color,
    criticalColor: Color,
    animatedProgress: Float
) {
    val width = size.width
    val height = size.height
    val padding = 40f
    
    val chartWidth = width - padding * 2
    val chartHeight = height - padding * 2
    
    if (temperatureHistory.isEmpty()) return
    
    // رسم خطوط مرجع دما
    drawTemperatureReferences(
        padding, chartWidth, chartHeight, maxTemperature,
        warningTemperature, criticalTemperature,
        warningColor, criticalColor
    )
    
    // رسم خطوط شبکه
    drawGridLines(padding, chartWidth, chartHeight)
    
    // رسم منحنی دما
    drawTemperatureCurve(
        temperatureHistory, maxTemperature,
        padding, chartWidth, chartHeight,
        normalColor, warningColor, criticalColor,
        warningTemperature, criticalTemperature,
        animatedProgress
    )
    
    // رسم برچسب‌های محورها
    drawAxisLabels(padding, chartWidth, chartHeight, maxTemperature, temperatureHistory.size)
}

/**
 * رسم خطوط مرجع دما
 */
private fun DrawScope.drawTemperatureReferences(
    padding: Float,
    chartWidth: Float,
    chartHeight: Float,
    maxTemperature: Double,
    warningTemperature: Double,
    criticalTemperature: Double,
    warningColor: Color,
    criticalColor: Color
) {
    val strokeWidth = 2.dp.toPx()
    
    // خط هشدار
    val warningY = padding + chartHeight - (warningTemperature / maxTemperature * chartHeight).toFloat()
    drawLine(
        color = warningColor.copy(alpha = 0.6f),
        start = Offset(padding, warningY),
        end = Offset(padding + chartWidth, warningY),
        strokeWidth = strokeWidth,
        pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 5f))
    )
    
    // خط بحرانی
    val criticalY = padding + chartHeight - (criticalTemperature / maxTemperature * chartHeight).toFloat()
    drawLine(
        color = criticalColor.copy(alpha = 0.6f),
        start = Offset(padding, criticalY),
        end = Offset(padding + chartWidth, criticalY),
        strokeWidth = strokeWidth,
        pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 5f))
    )
}

/**
 * رسم منحنی دما
 */
private fun DrawScope.drawTemperatureCurve(
    temperatureHistory: List<Double>,
    maxTemperature: Double,
    padding: Float,
    chartWidth: Float,
    chartHeight: Float,
    normalColor: Color,
    warningColor: Color,
    criticalColor: Color,
    warningTemperature: Double,
    criticalTemperature: Double,
    animatedProgress: Float
) {
    if (temperatureHistory.size < 2) return
    
    val strokeWidth = 4.dp.toPx()
    
    // محاسبه نقاط
    val points = temperatureHistory.mapIndexed { index, temp ->
        val x = padding + (chartWidth * index / (temperatureHistory.size - 1).coerceAtLeast(1))
        val normalizedTemp = (temp / maxTemperature).coerceIn(0.0, 1.0)
        val y = padding + chartHeight - (normalizedTemp * chartHeight).toFloat()
        Pair(Offset(x, y), temp)
    }
    
    // اعمال انیمیشن
    val animatedPointCount = (points.size * animatedProgress).toInt().coerceAtLeast(1)
    val animatedPoints = points.take(animatedPointCount)
    
    if (animatedPoints.isNotEmpty()) {
        // رسم gradient fill
        val path = Path()
        path.moveTo(animatedPoints[0].first.x, padding + chartHeight)
        path.lineTo(animatedPoints[0].first.x, animatedPoints[0].first.y)
        
        for (i in 1 until animatedPoints.size) {
            path.lineTo(animatedPoints[i].first.x, animatedPoints[i].first.y)
        }
        
        path.lineTo(animatedPoints.last().first.x, padding + chartHeight)
        path.close()
        
        // gradient برای fill
        val gradient = Brush.verticalGradient(
            colors = listOf(
                normalColor.copy(alpha = 0.3f),
                normalColor.copy(alpha = 0.1f)
            ),
            startY = padding,
            endY = padding + chartHeight
        )
        
        drawPath(path, brush = gradient)
        
        // رسم خط اصلی با رنگ‌های متغیر
        for (i in 0 until animatedPoints.size - 1) {
            val startPoint = animatedPoints[i]
            val endPoint = animatedPoints[i + 1]
            val avgTemp = (startPoint.second + endPoint.second) / 2
            
            val lineColor = when {
                avgTemp >= criticalTemperature -> criticalColor
                avgTemp >= warningTemperature -> warningColor
                else -> normalColor
            }
            
            drawLine(
                color = lineColor,
                start = startPoint.first,
                end = endPoint.first,
                strokeWidth = strokeWidth,
                cap = StrokeCap.Round
            )
        }
        
        // حذف رسم نقاط برای ظاهر خطی ساده
    }
}

/**
 * رسم خطوط شبکه
 */
private fun DrawScope.drawGridLines(
    padding: Float,
    chartWidth: Float,
    chartHeight: Float
) {
    val gridColor = Color.Gray.copy(alpha = 0.2f)
    val strokeWidth = 1.dp.toPx()
    
    // خطوط افقی
    for (i in 0..4) {
        val y = padding + (chartHeight * i / 4)
        drawLine(
            color = gridColor,
            start = Offset(padding, y),
            end = Offset(padding + chartWidth, y),
            strokeWidth = strokeWidth
        )
    }
    
    // خطوط عمودی
    for (i in 0..4) {
        val x = padding + (chartWidth * i / 4)
        drawLine(
            color = gridColor,
            start = Offset(x, padding),
            end = Offset(x, padding + chartHeight),
            strokeWidth = strokeWidth
        )
    }
}

/**
 * رسم برچسب‌های محورها
 */
private fun DrawScope.drawAxisLabels(
    padding: Float,
    chartWidth: Float,
    chartHeight: Float,
    maxTemperature: Double,
    dataPoints: Int
) {
    val textPaint = android.graphics.Paint().apply {
        color = Color.Gray.toArgb()
        textSize = 12.sp.toPx()
        isAntiAlias = true
    }
    
    // برچسب‌های دما (محور Y)
    for (i in 0..4) {
        val temp = (maxTemperature * (4 - i) / 4).toInt()
        val y = padding + (chartHeight * i / 4)
        
        drawContext.canvas.nativeCanvas.drawText(
            "${temp}°C",
            padding - 30.dp.toPx(),
            y + 4.dp.toPx(),
            textPaint
        )
    }
    
    // برچسب‌های زمان (محور X)
    for (i in 0..4) {
        val timeLabel = "${i * dataPoints / 4}s"
        val x = padding + (chartWidth * i / 4)
        
        drawContext.canvas.nativeCanvas.drawText(
            timeLabel,
            x - 8.dp.toPx(),
            padding + chartHeight + 20.dp.toPx(),
            textPaint
        )
    }
}

/**
 * آمار دما
 */
@Composable
private fun TemperatureStats(
    temperatureHistory: List<Double>,
    warningTemperature: Double,
    criticalTemperature: Double,
    normalColor: Color,
    warningColor: Color,
    criticalColor: Color
) {
    if (temperatureHistory.isNotEmpty()) {
        val avgTemp = temperatureHistory.average()
        val maxTemp = temperatureHistory.maxOrNull() ?: 0.0
        val minTemp = temperatureHistory.minOrNull() ?: 0.0
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            TempStatItem("میانگین", "${avgTemp.toInt()}°C", normalColor)
            TempStatItem("حداکثر", "${maxTemp.toInt()}°C", 
                when {
                    maxTemp >= criticalTemperature -> criticalColor
                    maxTemp >= warningTemperature -> warningColor
                    else -> normalColor
                }
            )
            TempStatItem("حداقل", "${minTemp.toInt()}°C", normalColor)
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // راهنمای رنگ‌ها
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            LegendItem("عادی", normalColor)
            LegendItem("هشدار", warningColor)
            LegendItem("بحرانی", criticalColor)
        }
    }
}

@Composable
private fun TempStatItem(label: String, value: String, color: Color) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun LegendItem(label: String, color: Color) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(10.dp)
                .background(color, RoundedCornerShape(2.dp))
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
