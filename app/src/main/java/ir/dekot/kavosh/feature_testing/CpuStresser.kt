package ir.dekot.kavosh.feature_testing

import ir.dekot.kavosh.feature_testing.model.CpuStressTestConfig
import ir.dekot.kavosh.feature_testing.model.CpuTestType
import ir.dekot.kavosh.feature_testing.model.TestIntensity
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import java.lang.Math.pow
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.*
import kotlin.math.pow

class CpuStresser {
    private var stressJobs: List<Job> = emptyList()
    private val isRunning = AtomicBoolean(false)
    private var config: CpuStressTestConfig = CpuStressTestConfig()
    private var customDispatcher: ExecutorCoroutineDispatcher? = null
    
    // کانال برای کنترل تعداد تردهای فعال
    private var workerChannel: Channel<Unit>? = null

    fun start(config: CpuStressTestConfig) {
        if (isRunning.get()) stop()

        this.config = config
        isRunning.set(true)

        val coreCount = if (config.targetCores == -1)
            Runtime.getRuntime().availableProcessors()
        else config.targetCores.coerceAtMost(Runtime.getRuntime().availableProcessors())

        // ایجاد dispatcher سفارشی محدود به تعداد هسته‌های مشخص شده
        createLimitedDispatcher(coreCount)
        
        // ایجاد کانال برای کنترل همزمانی
        workerChannel = Channel(capacity = coreCount)
        
        // پر کردن کانال با تیکت‌های مجاز
        repeat(coreCount) {
            workerChannel?.trySend(Unit)
        }

        stressJobs = when (config.testType) {
            CpuTestType.SINGLE_CORE -> {
                // فقط یک هسته - کاربر نمی‌تواند تعداد هسته را تغییر دهد
                // از Dispatcher محدود به یک thread استفاده می‌کنیم
                createSingleCoreDispatcher()
                listOf(
                    CoroutineScope(customDispatcher ?: Dispatchers.Default.limitedParallelism(1)).launch {
                        runSingleCoreStressTest()
                    }
                )
            }
            CpuTestType.MULTI_CORE -> {
                // چند هسته - کاربر می‌تواند تعداد هسته‌ها را انتخاب کند
                // هر هسته کار مختلف انجام می‌دهد تا تفاوت محسوس باشد
                (0 until coreCount).map { coreIndex ->
                    CoroutineScope(customDispatcher ?: Dispatchers.Default).launch {
                        runMultiCoreStressTest(coreIndex, coreCount)
                    }
                }
            }
            CpuTestType.STABILITY -> {
                // تست پایداری - بار متغیر در طی زمان
                // از الگوهای مختلف بار استفاده می‌کند
                (0 until coreCount).map { coreIndex ->
                    CoroutineScope(customDispatcher ?: Dispatchers.Default).launch {
                        runStabilityTest(coreIndex)
                    }
                }
            }
            CpuTestType.PEAK_PERFORMANCE -> {
                // حداکثر عملکرد - بدون محدودیت و با حداکثر فشار
                // از تکنیک‌های پیشرفته CPU-intensive استفاده می‌کند
                (0 until coreCount).map { coreIndex ->
                    CoroutineScope(customDispatcher ?: Dispatchers.Default).launch {
                        runPeakPerformanceTest(coreIndex)
                    }
                }
            }
        }
    }
    
    // سازگاری با کد قبلی
    fun start(coreCount: Int) {
        val config = CpuStressTestConfig(
            testType = CpuTestType.MULTI_CORE,
            targetCores = coreCount,
            intensity = TestIntensity.HIGH
        )
        start(config)
    }

    /**
     * تست تک هسته - فقط روی یک هسته کار می‌کند
     * با الگوهای محاسباتی سنگین و مداوم
     */
    private suspend fun runSingleCoreStressTest() {
        val baseOperations = getOperationsForIntensity(config.intensity)
        var iteration = 0
        
        while (isRunning.get() && currentCoroutineContext().isActive) {
            // محاسبات سنگین مخصوص تک هسته
            performSingleCoreOperations(baseOperations, iteration)
            
            // در تست تک هسته delay کمتر برای فشار بیشتر
            if (config.intensity != TestIntensity.EXTREME) {
                delay(2)
            }
            iteration++
        }
    }

    /**
     * تست چند هسته - هر هسته کار متفاوتی انجام می‌دهد
     * برای تست توزیع بار بین هسته‌ها
     */
    private suspend fun runMultiCoreStressTest(coreIndex: Int, totalCores: Int) {
        val permit = workerChannel?.receive()
        try {
            val baseOperations = getOperationsForIntensity(config.intensity)
            var iteration = 0
            
            while (isRunning.get() && currentCoroutineContext().isActive) {
                // هر هسته نوع محاسبات متفاوتی انجام می‌دهد
                performMultiCoreOperations(baseOperations, coreIndex, totalCores, iteration)
                
                // delay متفاوت برای هر هسته تا الگوی جالبی ایجاد شود
                val coreSpecificDelay = when (config.intensity) {
                    TestIntensity.LOW -> 10 + (coreIndex * 2)
                    TestIntensity.MEDIUM -> 5 + coreIndex
                    TestIntensity.HIGH -> 2 + (coreIndex / 2)
                    TestIntensity.EXTREME -> 0
                }
                
                if (coreSpecificDelay > 0) delay(coreSpecificDelay.toLong())
                iteration++
            }
        } finally {
            workerChannel?.trySend(Unit)
        }
    }

    /**
     * تست پایداری - بار متغیر در طی زمان
     * شبیه‌سازی استفاده واقعی سیستم
     */
    private suspend fun runStabilityTest(coreIndex: Int) {
        val permit = workerChannel?.receive()
        try {
            // الگوهای مختلف بار برای تست پایداری
            val stabilityPhases = listOf(
                0.2f, 0.4f, 0.7f, 0.9f, 1.0f, 0.8f, 0.5f, 0.3f,
                0.6f, 0.9f, 0.7f, 0.4f, 0.8f, 1.0f, 0.6f, 0.2f
            )
            
            var phaseIndex = 0
            val phaseTimeMs = when (config.intensity) {
                TestIntensity.LOW -> 8000L
                TestIntensity.MEDIUM -> 5000L
                TestIntensity.HIGH -> 3000L
                TestIntensity.EXTREME -> 2000L
            }

            while (isRunning.get() && currentCoroutineContext().isActive) {
                val currentPhase = stabilityPhases[phaseIndex % stabilityPhases.size]
                val operations = (getOperationsForIntensity(config.intensity) * currentPhase).toInt()
                
                // محاسبات با شدت متغیر
                performStabilityOperations(operations, coreIndex, currentPhase)
                
                // تغییر فاز
                delay(phaseTimeMs)
                phaseIndex++
            }
        } finally {
            workerChannel?.trySend(Unit)
        }
    }

    /**
     * تست حداکثر عملکرد - حداکثر فشار ممکن
     * بدون محدودیت و با تکنیک‌های پیشرفته
     */
    private suspend fun runPeakPerformanceTest(coreIndex: Int) {
        val permit = workerChannel?.receive()
        try {
            val maxOperations = getOperationsForIntensity(TestIntensity.EXTREME) * 2
            var iteration = 0
            
            while (isRunning.get() && currentCoroutineContext().isActive) {
                // حداکثر محاسبات ممکن
                performPeakOperations(maxOperations, coreIndex, iteration)
                
                // هیچ delay نداریم - حداکثر فشار
                // فقط yield می‌کنیم تا coroutine cancellation چک شود
                yield()
                iteration++
            }
        } finally {
            workerChannel?.trySend(Unit)
        }
    }

    private fun performCpuIntensiveOperations(operations: Int) {
        var result = 1.0
        repeat(operations) {
            result = sqrt(result * 1.000001 + sin(result) + cos(result))
            if (result.isInfinite() || result.isNaN()) result = 1.0
        }
    }

    private fun performVariableIntensityOperations(intensity: Float) {
        val operations = (1000 * intensity).toInt()
        performCpuIntensiveOperations(operations)
    }

    private fun getOperationsForIntensity(intensity: TestIntensity): Int {
        return when (intensity) {
            TestIntensity.LOW -> 1000
            TestIntensity.MEDIUM -> 5000
            TestIntensity.HIGH -> 10000
            TestIntensity.EXTREME -> 50000
        }
    }
    
    /**
     * ایجاد dispatcher محدود به تعداد هسته‌های مشخص شده
     */
    private fun createLimitedDispatcher(coreCount: Int) {
        // بستن dispatcher قبلی اگر وجود دارد
        customDispatcher?.close()
        
        val threadFactory = object : ThreadFactory {
            private val threadNumber = AtomicInteger(1)
            
            override fun newThread(r: Runnable): Thread {
                val thread = Thread(r, "CpuStress-${threadNumber.getAndIncrement()}")
                thread.isDaemon = true
                // تنظیم اولویت بالا برای تست استرس
                thread.priority = Thread.MAX_PRIORITY
                return thread
            }
        }
        
        // ایجاد thread pool محدود
        val executor = Executors.newFixedThreadPool(coreCount, threadFactory)
        customDispatcher = executor.asCoroutineDispatcher()
    }
    
    /**
     * عملیات CPU-intensive بهبود یافته با کنترل بهتر بار
     */
    private fun performAdvancedCpuIntensiveOperations(operations: Int, coreIndex: Int = 0) {
        var result = 1.0
        val baseOperations = operations
        
        // تنظیم بار بر اساس شماره هسته برای توزیع بهتر
        val adjustedOperations = when (config.testType) {
            CpuTestType.SINGLE_CORE -> baseOperations
            else -> baseOperations + (coreIndex * 100) // کمی تنوع در بار هر هسته
        }
        
        repeat(adjustedOperations) { i ->
            // ترکیب عملیات ریاضی مختلف برای بار واقعی‌تر
            when (i % 4) {
                0 -> result = sqrt(result * 1.000001 + sin(result))
                1 -> result = cos(result) + ln(max(1.0, abs(result)))
                2 -> result = tan(result * 0.1) + exp(result * 0.001)
                3 -> result = abs(result).pow(0.5) + atan(result)
            }
            
            // جلوگیری از overflow/underflow
            if (result.isInfinite() || result.isNaN() || abs(result) > 1e10) {
                result = 1.0 + (i % 100) * 0.01
            }
        }
    }
    
    /**
     * ایجاد dispatcher محدود به یک thread برای تست تک هسته
     */
    private fun createSingleCoreDispatcher() {
        customDispatcher?.close()
        val executor = Executors.newFixedThreadPool(1) { runnable ->
            val thread = Thread(runnable, "SingleCore-CpuStress")
            thread.isDaemon = true
            thread.priority = Thread.MAX_PRIORITY
            thread
        }
        customDispatcher = executor.asCoroutineDispatcher()
    }
    
    /**
     * محاسبات مخصوص تست تک هسته
     * عملیات پیچیده و سنگین برای تست قدرت یک هسته
     */
    private fun performSingleCoreOperations(operations: Int, iteration: Int) {
        var result = 1.0 + (iteration % 1000) * 0.001
        
        repeat(operations) { i ->
            // محاسبات پیچیده مخصوص تک هسته
            when (i % 8) {
                0 -> result = sqrt(result * 1.000001 + sin(result * 0.1))
                1 -> result = cos(result * 0.5) + ln(max(1.0, abs(result)))
                2 -> result = tan(result * 0.01) + exp(result * 0.0001)
                3 -> result = abs(result).pow(0.33) + atan(result * 0.1)
                4 -> result = sinh(result * 0.001) + cosh(result * 0.0001)
                5 -> result = asin(max(-0.99, min(0.99, result * 0.1))) * 10
                6 -> result = acos(max(-0.99, min(0.99, result * 0.01))) * 100
                7 -> result = atan2(result * 0.1, result * 0.01 + 1.0) * 1000
            }
            
            // محاسبات اضافی برای سنگین‌تر کردن
            if (i % 100 == 0) {
                var matrixSum = 0.0
                repeat(50) { j ->
                    repeat(50) { k ->
                        matrixSum += sin(j.toDouble()) * cos(k.toDouble()) * result * 0.00001
                    }
                }
                result += matrixSum * 0.001
            }
            
            // جلوگیری از overflow/underflow
            if (result.isInfinite() || result.isNaN() || abs(result) > 1e8) {
                result = 1.0 + (i % 1000) * 0.001
            }
        }
    }
    
    /**
     * محاسبات مخصوص تست چند هسته
     * هر هسته نوع محاسبات متفاوتی انجام می‌دهد
     */
    private fun performMultiCoreOperations(operations: Int, coreIndex: Int, totalCores: Int, iteration: Int) {
        var result = 1.0 + coreIndex * 0.1 + (iteration % 100) * 0.01
        val coreSpecificOperations = operations + (coreIndex * operations / totalCores)
        
        repeat(coreSpecificOperations) { i ->
            // هر هسته نوع محاسبات مختلفی انجام می‌دهد
            when (coreIndex % 4) {
                0 -> { // هسته‌های 0, 4, 8, ... : محاسبات مثلثاتی
                    result = sin(result + i * 0.001) + cos(result * 0.1) + tan(result * 0.01)
                }
                1 -> { // هسته‌های 1, 5, 9, ... : محاسبات لگاریتمی و نمایی
                    result = exp(result * 0.001) + ln(max(1.0, abs(result))) + result.pow(0.1)
                }
                2 -> { // هسته‌های 2, 6, 10, ... : محاسبات هیپربولیک
                    result = sinh(result * 0.01) + cosh(result * 0.001) + tanh(result * 0.1)
                }
                3 -> { // هسته‌های 3, 7, 11, ... : محاسبات ترکیبی
                    result = sqrt(abs(result) + 1.0) + atan(result * 0.1) + ceil(result * 0.01)
                }
            }
            
            // محاسبات اضافی هر 200 تکرار
            if (i % 200 == 0) {
                val specialCalc = (0 until 30).sumOf { j ->
                    sin(j * result * 0.001) + cos(j * coreIndex * 0.1)
                }
                result += specialCalc * 0.0001
            }
            
            // جلوگیری از overflow/underflow
            if (result.isInfinite() || result.isNaN() || abs(result) > 1e6) {
                result = 1.0 + coreIndex * 0.1 + (i % 100) * 0.01
            }
        }
    }
    
    /**
     * محاسبات مخصوص تست پایداری
     * بار متغیر با الگوهای مختلف
     */
    private fun performStabilityOperations(operations: Int, coreIndex: Int, phaseIntensity: Float) {
        var result = 1.0 + coreIndex * 0.05 + phaseIntensity
        val adjustedOperations = (operations * phaseIntensity).toInt()
        
        repeat(adjustedOperations) { i ->
            // محاسبات بر اساس شدت فاز
            val complexity = (phaseIntensity * 8).toInt()
            when (complexity) {
                in 0..1 -> { // فاز آرام
                    result = sqrt(result + 0.1) + sin(result * 0.01)
                }
                in 2..3 -> { // فاز متوسط
                    result = cos(result * 0.1) + ln(max(1.0, abs(result * 0.1)))
                }
                in 4..5 -> { // فاز سنگین
                    result = tan(result * 0.01) + exp(result * 0.001) + result.pow(0.2)
                }
                in 6..7 -> { // فاز شدید
                    result = sinh(result * 0.01) + cosh(result * 0.001) + atan(result)
                }
                else -> { // فاز حداکثر
                    result = sin(result) + cos(result) + tan(result * 0.1) + exp(result * 0.0001)
                }
            }
            
            // تست پایداری: محاسبات اضافی در برخی فازها
            if (phaseIntensity > 0.7f && i % 50 == 0) {
                var stabilitySum = 0.0
                repeat(20) { j ->
                    stabilitySum += sin(j * result * phaseIntensity * 0.01)
                }
                result += stabilitySum * 0.001
            }
            
            // جلوگیری از overflow/underflow
            if (result.isInfinite() || result.isNaN() || abs(result) > 1e7) {
                result = 1.0 + phaseIntensity + (i % 100) * 0.01
            }
        }
    }
    
    /**
     * محاسبات مخصوص تست حداکثر عملکرد
     * حداکثر محاسبات ممکن بدون محدودیت
     */
    private fun performPeakOperations(operations: Int, coreIndex: Int, iteration: Int) {
        var result = 1.0 + coreIndex + (iteration % 10000) * 0.0001
        
        repeat(operations) { i ->
            // تمام نوع محاسبات سنگین همزمان
            result = sin(result * 0.1) + cos(result * 0.05) + tan(result * 0.01) +
                    exp(result * 0.0001) + ln(max(1.0, abs(result * 0.1))) +
                    sinh(result * 0.001) + cosh(result * 0.0001) + tanh(result * 0.01) +
                    sqrt(abs(result) + 1.0) + result.pow(0.1) + atan(result * 0.1)
            
            // محاسبات ماتریسی سنگین هر 100 تکرار
            if (i % 100 == 0) {
                var matrixResult = 0.0
                repeat(10) { row ->
                    repeat(10) { col ->
                        matrixResult += sin(row * result * 0.01) * cos(col * coreIndex * 0.1) *
                                exp((row + col) * 0.001) + tan((row * col + 1) * result * 0.001)
                    }
                }
                result += matrixResult * 0.0001
                
                // محاسبات فیبوناچی برای فشار اضافی
                var fib1 = 1.0
                var fib2 = 1.0
                repeat(30) {
                    val temp = fib1 + fib2 + result * 0.000001
                    fib1 = fib2
                    fib2 = temp
                }
                result += fib2 * 0.000001
            }
            
            // جلوگیری از overflow/underflow
            if (result.isInfinite() || result.isNaN() || abs(result) > 1e5) {
                result = 1.0 + coreIndex + (i % 1000) * 0.001
            }
        }
    }

    fun stop() {
        isRunning.set(false)
        stressJobs.forEach { it.cancel() }
        stressJobs = emptyList()
        
        // بستن کانال و dispatcher
        workerChannel?.close()
        workerChannel = null
        
        customDispatcher?.close()
        customDispatcher = null
    }
}
