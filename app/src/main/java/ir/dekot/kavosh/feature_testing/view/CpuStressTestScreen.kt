package ir.dekot.kavosh.feature_testing.view

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import ir.dekot.kavosh.R
import ir.dekot.kavosh.core.ui.shared_components.KavoshTopAppBar
import ir.dekot.kavosh.feature_deviceInfo.model.CpuInfo
import ir.dekot.kavosh.feature_deviceInfo.view.InfoRow
import ir.dekot.kavosh.feature_testing.model.CpuStressTestConfig
import ir.dekot.kavosh.feature_testing.model.CpuTestType
import ir.dekot.kavosh.feature_testing.model.PerformancePattern
import ir.dekot.kavosh.feature_testing.model.PerformanceStatistics
import ir.dekot.kavosh.feature_testing.model.RealTimeData
import ir.dekot.kavosh.feature_testing.model.TestIntensity
import ir.dekot.kavosh.feature_testing.model.ThermalStatus
import ir.dekot.kavosh.feature_testing.viewModel.CpuStressTestViewModel
import ir.dekot.kavosh.feature_testing.viewModel.TestStatus

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CpuStressTestScreen(
    viewModel: CpuStressTestViewModel = hiltViewModel(),
    onBackClick: () -> Unit
) {
    val isTesting by viewModel.isTesting.collectAsState()
    val cpuInfo by viewModel.cpuInfo.collectAsState()
    val liveFrequencies by viewModel.liveFrequencies.collectAsState()
    val realTimeData by viewModel.realTimeData.collectAsState()
    val testConfig by viewModel.testConfig.collectAsState()
    val performanceStats by viewModel.performanceStats.collectAsState()
    val testStatus by viewModel.testStatus.collectAsState()
    val statusMessage by viewModel.statusMessage.collectAsState()

    val sleepingText = stringResource(R.string.label_sleeping)

    Scaffold(
        topBar = {
            // استفاده از نوار بالایی سفارشی برای یکپارچگی رنگی
            KavoshTopAppBar(
                title = stringResource(R.string.cpu_stress_test_title),
                onBackClick = onBackClick
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // کارت اطلاعات CPU
            item {
                CpuInfoCard(cpuInfo = cpuInfo)
            }

            // مانیتور زنده (هنگام تست)
            item {
                LiveTestMonitor(
                    realTimeData = realTimeData,
                    testStatus = testStatus,
                    isVisible = isTesting
                )
            }

            // کارت آمار زنده
            item {
                RealTimeStatsCard(
                    realTimeData = realTimeData,
                    isVisible = isTesting
                )
            }

            // کارت تنظیمات تست
            if (!isTesting) {
                item {
                    TestConfigurationCard(
                        config = testConfig,
                        onConfigChange = { viewModel.updateTestConfig(it) }
                    )
                }
            }

            // دکمه شروع/توقف
            item {
                TestControlButton(
                    isTesting = isTesting,
                    testStatus = testStatus,
                    statusMessage = statusMessage,
                    onToggle = { viewModel.onTestToggle() }
                )
            }

            // زمان‌سنج (هنگام تست)
            if (isTesting) {
                item {
                    TestTimer(
                        testConfig = testConfig,
                        testStartTime = viewModel.getTestStartTime(),
                        currentTime = System.currentTimeMillis()
                    )
                }
            }

            // پیش‌نمایش کوچک نمودارها (هنگام تست)
            if (isTesting) {
                item {
                    MiniChartPreview(
                        frequencyHistory = viewModel.getChartData().frequencyHistory,
                        temperatureHistory = viewModel.getChartData().temperatureHistory
                    )
                }
            }

            // کارت فرکانس هسته‌ها
            item {
                val activeCoreCount = if (testConfig.targetCores == -1) {
                    cpuInfo.coreCount
                } else {
                    testConfig.targetCores.coerceAtMost(cpuInfo.coreCount)
                }
                
                // اطمینان از یکپارچگی تعداد فرکانس‌ها
                val displayFrequencies = if (isTesting) {
                    // فقط فرکانس‌های هسته‌های فعال را نمایش می‌دهیم
                    liveFrequencies.take(activeCoreCount).let { frequencies ->
                        // اگر تعداد فرکانس‌ها کمتر از تعداد هسته‌های فعال باشد، باقی را با مقدار پیش‌فرض پر می‌کنیم
                        if (frequencies.size < activeCoreCount) {
                            frequencies + List(activeCoreCount - frequencies.size) { "0 MHz" }
                        } else {
                            frequencies
                        }
                    }
                } else {
                    List(activeCoreCount) { sleepingText }
                }
                
                CoreFrequenciesCard(
                    frequencies = displayFrequencies,
                    maxFrequencies = cpuInfo.maxFrequenciesKhz.take(activeCoreCount),
                    perCoreUsage = realTimeData.perCoreUsage.take(activeCoreCount),
                    isVisible = isTesting || displayFrequencies.isNotEmpty(),
                    activeCoreCount = activeCoreCount,
                    totalCoreCount = cpuInfo.coreCount
                )
            }

            // نمودارهای real-time (هنگام تست) - فقط اگر داده موجود باشد
            if (isTesting && realTimeData.frequencies.isNotEmpty()) {
                val chartData = viewModel.getChartData()

                item(key = "frequency_chart") {
                    CpuFrequencyChart(
                        frequencyHistory = chartData.frequencyHistory.takeLast(30), // فقط ۳۰ نمونه آخر
                        maxFrequencies = cpuInfo.maxFrequenciesKhz,
                        animationDuration = 200 // انیمیشن سریع‌تر
                    )
                }

                item(key = "temperature_chart") {
                    CpuTemperatureChart(
                        temperatureHistory = chartData.temperatureHistory.takeLast(30), // فقط ۳۰ نمونه آخر
                        animationDuration = 200 // انیمیشن سریع‌تر
                    )
                }

                item(key = "performance_chart") {
                    CpuPerformanceChart(
                        performanceScore = realTimeData.performanceScore,
                        stabilityScore = 0, // محاسبه در زمان واقعی
                        cpuUsage = realTimeData.cpuUsage,
                        temperature = realTimeData.temperature,
                        animationDuration = 200 // انیمیشن سریع‌تر
                    )
                }
            }

            // کارت آمار عملکرد (فقط بعد از تست)
            performanceStats?.let { stats ->
                item {
                    PerformanceStatsCard(stats = stats)
                }

                // نمودارهای نتایج نهایی
                val finalChartData = viewModel.getChartData()

                if (finalChartData.frequencyHistory.isNotEmpty()) {
                    item {
                        CpuFrequencyChart(
                            frequencyHistory = finalChartData.frequencyHistory,
                            maxFrequencies = cpuInfo.maxFrequenciesKhz,
                            animationDuration = 500
                        )
                    }
                }

                if (finalChartData.temperatureHistory.isNotEmpty()) {
                    item {
                        CpuTemperatureChart(
                            temperatureHistory = finalChartData.temperatureHistory,
                            animationDuration = 500
                        )
                    }
                }

                item {
                    CpuPerformanceChart(
                        performanceScore = stats.performanceScore,
                        stabilityScore = stats.stabilityScore,
                        cpuUsage = stats.usageStats.average,
                        temperature = stats.temperatureStats.average
                    )
                }

                item {
                    CpuComparisonChart(
                        currentScores = mapOf(
                            "عملکرد" to stats.performanceScore,
                            "پایداری" to stats.stabilityScore,
                            "کارایی" to ((stats.usageStats.average / 100f) * 100).toInt(),
                            "مدیریت حرارت" to getThermalManagementScore(stats.temperatureStats.average)
                        )
                    )
                }
            }
        }
    }
}

/**
 * کارت اطلاعات CPU
 */
@Composable
private fun CpuInfoCard(cpuInfo: CpuInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "اطلاعات پردازنده",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            InfoRow(label = stringResource(R.string.cpu_model), value = cpuInfo.model)
            InfoRow(label = stringResource(R.string.cpu_core_count), value = cpuInfo.coreCount.toString())
            InfoRow(label = "معماری", value = cpuInfo.architecture)
        }
    }
}

/**
 * کارت آمار زنده
 */
@Composable
private fun RealTimeStatsCard(
    realTimeData: RealTimeData,
    isVisible: Boolean
) {
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically() + fadeIn(),
        exit = slideOutVertically() + fadeOut()
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "آمار زنده",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )

                Spacer(modifier = Modifier.height(12.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatItem(
                        label = stringResource(R.string.cpu_usage),
                        value = "${realTimeData.cpuUsage.toInt()}%",
                        color = getUsageColor(realTimeData.cpuUsage)
                    )
                    StatItem(
                        label = stringResource(R.string.cpu_temperature),
                        value = "${realTimeData.temperature.toInt()}°C",
                        color = getTemperatureColor(realTimeData.temperature)
                    )
                    StatItem(
                        label = stringResource(R.string.performance_score),
                        value = "${realTimeData.performanceScore}",
                        color = getScoreColor(realTimeData.performanceScore)
                    )
                }

                if (realTimeData.powerConsumption > 0) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        StatItem(
                            label = stringResource(R.string.power_consumption),
                            value = "${"%.1f".format(realTimeData.powerConsumption)}W",
                            color = MaterialTheme.colorScheme.secondary
                        )
                        StatItem(
                            label = "وضعیت حرارتی",
                            value = getThermalStatusText(realTimeData.thermalStatus),
                            color = getThermalStatusColor(realTimeData.thermalStatus)
                        )
                        if (realTimeData.isThrottling) {
                            StatItem(
                                label = "هشدار",
                                value = "Throttling",
                                color = Color.Red
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * دکمه کنترل تست
 */
@Composable
private fun TestControlButton(
    isTesting: Boolean,
    testStatus: TestStatus,
    statusMessage: String,
    onToggle: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
    ) {
        Button(
            onClick = onToggle,
            colors = ButtonDefaults.buttonColors(
                containerColor = if (isTesting) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary
            ),
            modifier = Modifier.fillMaxWidth(0.8f),
            enabled = testStatus != TestStatus.PREPARING && testStatus != TestStatus.STOPPING,
            shape = RoundedCornerShape(12.dp)
        ) {
            Icon(
                imageVector = if (isTesting) Icons.Default.Stop else Icons.Default.PlayArrow,
                contentDescription = if (isTesting) stringResource(R.string.stop_test) else stringResource(R.string.start_test)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = if (isTesting) stringResource(R.string.stop_test) else stringResource(R.string.start_test),
                style = MaterialTheme.typography.labelLarge
            )
        }

        if (statusMessage.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = statusMessage,
                style = MaterialTheme.typography.bodyMedium,
                color = getStatusColor(testStatus),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * کارت فرکانس هسته‌ها
 */
@Composable
private fun CoreFrequenciesCard(
    frequencies: List<String>,
    maxFrequencies: List<Long>,
    perCoreUsage: List<Float>,
    isVisible: Boolean,
    activeCoreCount: Int = frequencies.size,
    totalCoreCount: Int = Runtime.getRuntime().availableProcessors()
) {
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically() + fadeIn(),
        exit = slideOutVertically() + fadeOut()
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "فرکانس هسته‌ها",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    if (activeCoreCount < totalCoreCount) {
                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.background
                            )
                        ) {
                            Text(
                                text = "$activeCoreCount از $totalCoreCount هسته فعال",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                frequencies.forEachIndexed { index, frequency ->
                    CoreFrequencyItem(
                        coreIndex = index,
                        frequency = frequency,
                        maxFrequencyKhz = maxFrequencies.getOrElse(index) { 0L },
                        usage = perCoreUsage.getOrElse(index) { 0f },
                        isActive = true
                    )
                    if (index < frequencies.size - 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
                
                // نمایش هسته‌های غیرفعال اگر وجود دارد
                if (activeCoreCount < totalCoreCount) {
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "هسته‌های غیرفعال:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    (activeCoreCount until totalCoreCount).forEach { coreIndex ->
                        CoreFrequencyItem(
                            coreIndex = coreIndex,
                            frequency = "غیرفعال",
                            maxFrequencyKhz = 0L,
                            usage = 0f,
                            isActive = false
                        )
                        if (coreIndex < totalCoreCount - 1) {
                            Spacer(modifier = Modifier.height(6.dp))
                        }
                    }
                }
            }
        }
    }
}

/**
 * آیتم فرکانس هسته (بهبود یافته)
 */
@Composable
private fun CoreFrequencyItem(
    coreIndex: Int,
    frequency: String,
    maxFrequencyKhz: Long,
    usage: Float = 0f,
    isActive: Boolean = true
) {
    val currentFreqMhz = frequency.substringBefore(" ").toLongOrNull() ?: 0L
    val maxFreqMhz = maxFrequencyKhz / 1000
    val freqProgress = if (maxFreqMhz > 0) (currentFreqMhz.toFloat() / maxFreqMhz) else 0f
    val usageProgress = usage / 100f

    val animatedFreqProgress by animateFloatAsState(targetValue = freqProgress, label = "FreqProgress$coreIndex")
    val animatedUsageProgress by animateFloatAsState(targetValue = usageProgress, label = "UsageProgress$coreIndex")

    val freqColor = when {
        animatedFreqProgress > 0.85f -> Color.Red
        animatedFreqProgress > 0.6f -> Color(0xFFFFA500) // Orange
        else -> MaterialTheme.colorScheme.primary
    }

    val usageColor = when {
        animatedUsageProgress > 0.8f -> Color.Red
        animatedUsageProgress > 0.6f -> Color(0xFFFFA500)
        else -> MaterialTheme.colorScheme.secondary
    }
    
    // تنظیم رنگ‌ها برای هسته‌های غیرفعال
    val displayFreqColor = if (isActive) freqColor else MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
    val displayUsageColor = if (isActive) usageColor else MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)

    Column(
        modifier = if (!isActive) Modifier.alpha(0.6f) else Modifier
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // نشانگر وضعیت هسته
                if (isActive) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                Color(0xFF4CAF50),
                                shape = CircleShape
                            )
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
                                shape = CircleShape
                            )
                    )
                }
                
                Text(
                    text = stringResource(R.string.cpu_core_prefix, coreIndex),
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = if (isActive) MaterialTheme.colorScheme.onSurface
                           else MaterialTheme.colorScheme.onSurface
                )
            }
            
            Column(horizontalAlignment = Alignment.End) {
                Text(
                    text = frequency,
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isActive) MaterialTheme.colorScheme.secondary
                           else MaterialTheme.colorScheme.onSurface
                )
                if (usage > 0 && isActive) {
                    Text(
                        text = "${usage.toInt()}%",
                        style = MaterialTheme.typography.bodySmall,
                        color = displayUsageColor
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(4.dp))

        // نوار فرکانس (فقط برای هسته‌های فعال)
        if (isActive) {
            LinearProgressIndicator(
                progress = { animatedFreqProgress },
                modifier = Modifier.fillMaxWidth().height(6.dp),
                color = displayFreqColor,
                trackColor = displayFreqColor.copy(alpha = 0.2f)
            )

            // نوار استفاده (اگر داده موجود باشد)
            if (usage > 0) {
                Spacer(modifier = Modifier.height(2.dp))
                LinearProgressIndicator(
                    progress = { animatedUsageProgress },
                    modifier = Modifier.fillMaxWidth().height(4.dp),
                    color = displayUsageColor,
                    trackColor = displayUsageColor.copy(alpha = 0.2f)
                )
            }
        } else {
            // نوار خالی برای هسته‌های غیرفعال
            LinearProgressIndicator(
                progress = { 0f },
                modifier = Modifier.fillMaxWidth().height(4.dp),
                color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                trackColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.1f)
            )
        }
    }
}

/**
 * کارت تنظیمات تست
 */
@Composable
private fun TestConfigurationCard(
    config: CpuStressTestConfig,
    onConfigChange: (CpuStressTestConfig) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "تنظیمات تست",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            // انتخاب نوع تست
            TestTypeSelector(
                selectedType = config.testType,
                onTypeSelected = { newType ->
                    // تنظیم خودکار پیکربندی بر اساس نوع تست
                    val newConfig = when (newType) {
                        CpuTestType.SINGLE_CORE -> config.copy(
                            testType = newType,
                            targetCores = 1 // اجباری یک هسته
                        )
                        CpuTestType.PEAK_PERFORMANCE -> config.copy(
                            testType = newType,
                            intensity = TestIntensity.EXTREME // اجباری حداکثر شدت
                        )
                        else -> config.copy(testType = newType)
                    }
                    onConfigChange(newConfig)
                }
            )

            Spacer(modifier = Modifier.height(12.dp))

            // انتخاب شدت (شرطی بر اساس نوع تست)
            if (config.testType != CpuTestType.PEAK_PERFORMANCE) {
                IntensitySelector(
                    testType = config.testType,
                    intensity = config.intensity,
                    onIntensityChange = { onConfigChange(config.copy(intensity = it)) }
                )
                Spacer(modifier = Modifier.height(12.dp))
            } else {
                // نمایش شدت ثابت برای تست حداکثر عملکرد
                FixedIntensityDisplay(intensity = TestIntensity.EXTREME)
                Spacer(modifier = Modifier.height(12.dp))
            }

            // تنظیم مدت زمان
            DurationSlider(
                duration = config.duration,
                onDurationChange = { onConfigChange(config.copy(duration = it)) }
            )

            Spacer(modifier = Modifier.height(12.dp))

            // انتخاب تعداد هسته‌ها (فقط برای تست‌های چند هسته‌ای)
            if (config.testType != CpuTestType.SINGLE_CORE) {
                CoreCountSelector(
                    targetCores = config.targetCores,
                    maxCores = Runtime.getRuntime().availableProcessors(),
                    testType = config.testType,
                    onCoreCountChange = { onConfigChange(config.copy(targetCores = it)) }
                )
                Spacer(modifier = Modifier.height(12.dp))
            } else {
                // نمایش تعداد هسته‌های ثابت برای تست تک هسته‌ای
                FixedCoreCountDisplay(coreCount = 1)
                Spacer(modifier = Modifier.height(12.dp))
            }

            // تنظیمات ایمنی
            SafetySettings(
                maxTemperature = config.maxTemperature,
                autoStop = config.autoStop,
                monitorTemperature = config.monitorTemperature,
                onMaxTemperatureChange = { onConfigChange(config.copy(maxTemperature = it)) },
                onAutoStopChange = { onConfigChange(config.copy(autoStop = it)) },
                onMonitorTemperatureChange = { onConfigChange(config.copy(monitorTemperature = it)) }
            )
        }
    }
}

/**
 * کارت آمار عملکرد
 */
@Composable
private fun PerformanceStatsCard(stats: PerformanceStatistics) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "نتایج تست",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = stringResource(R.string.performance_score),
                    value = "${stats.performanceScore}/100",
                    color = getScoreColor(stats.performanceScore)
                )
                StatItem(
                    label = stringResource(R.string.stability_score),
                    value = "${stats.stabilityScore}/100",
                    color = getScoreColor(stats.stabilityScore)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = "میانگین فرکانس",
                    value = "${(stats.frequencyStats.average / 1000).toInt()} MHz",
                    color = MaterialTheme.colorScheme.secondary
                )
                StatItem(
                    label = "میانگین دما",
                    value = "${stats.temperatureStats.average.toInt()}°C",
                    color = getTemperatureColor(stats.temperatureStats.average)
                )
            }

            if (stats.patterns.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "الگوهای تشخیص داده شده:",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                stats.patterns.forEach { pattern ->
                    Text(
                        text = "• ${getPatternText(pattern)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.secondary
                    )
                }
            }
        }
    }
}

/**
 * انتخابگر نوع تست
 */
@Composable
private fun TestTypeSelector(
    selectedType: CpuTestType,
    onTypeSelected: (CpuTestType) -> Unit
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = "نوع تست:",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            InfoIcon(
                infoText = """
                    انواع مختلف تست استرس پردازنده:
                    
                    ${getTestTypeDescription(CpuTestType.SINGLE_CORE)}
                    
                    ${getTestTypeDescription(CpuTestType.MULTI_CORE)}
                    
                    ${getTestTypeDescription(CpuTestType.STABILITY)}
                    
                    ${getTestTypeDescription(CpuTestType.PEAK_PERFORMANCE)}
                """.trimIndent()
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(CpuTestType.entries.toTypedArray()) { testType ->
                FilterChip(
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor =MaterialTheme.colorScheme.primary,
                        selectedLabelColor = MaterialTheme.colorScheme.onPrimary
                    ),
                    onClick = { onTypeSelected(testType) },
                    label = { Text(getTestTypeText(testType)) },
                    selected = selectedType == testType
                )
            }
        }
    }
}

/**
 * انتخابگر شدت تست
 */
@Composable
private fun IntensitySelector(
    testType: CpuTestType,
    intensity: TestIntensity,
    onIntensityChange: (TestIntensity) -> Unit
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = "شدت تست:",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            InfoIcon(
                infoText = """
                    سطوح مختلف شدت تست:
                    
                    ${getIntensityDescription(TestIntensity.LOW)}
                    
                    ${getIntensityDescription(TestIntensity.MEDIUM)}
                    
                    ${getIntensityDescription(TestIntensity.HIGH)}
                    
                    ${getIntensityDescription(TestIntensity.EXTREME)}
                """.trimIndent()
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(TestIntensity.entries.toTypedArray()) { testIntensity ->
                FilterChip(
                    onClick = { onIntensityChange(testIntensity) },
                    label = { Text(getIntensityText(testIntensity)) },
                    selected = intensity == testIntensity,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = getIntensityColor(testIntensity)
                    )
                )
            }
        }
    }
}

/**
 * اسلایدر مدت زمان
 */
@Composable
private fun DurationSlider(
    duration: Long,
    onDurationChange: (Long) -> Unit
) {
    Column {
        Text(
            text = "مدت زمان: ${duration / 1000} ثانیه",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Slider(
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.primary,
                activeTrackColor = MaterialTheme.colorScheme.primary,
                inactiveTrackColor = MaterialTheme.colorScheme.background
            ),
            value = (duration / 1000).toFloat(),
            onValueChange = { onDurationChange((it * 1000).toLong()) },
            valueRange = 10f..300f,
            steps = 28, // 10, 15, 20, 30, 45, 60, 90, 120, 180, 240, 300
            modifier = Modifier.fillMaxWidth()
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text("10s", style = MaterialTheme.typography.bodySmall)
            Text("5m", style = MaterialTheme.typography.bodySmall)
        }
    }
}

/**
 * آیتم آمار
 */
@Composable
private fun StatItem(
    label: String,
    value: String,
    color: Color = MaterialTheme.colorScheme.onSurface
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * کامپوننت آیکون اطلاعات با tooltip
 */
@Composable
private fun InfoIcon(
    infoText: String,
    modifier: Modifier = Modifier
) {
    var showDialog by remember { mutableStateOf(false) }

    IconButton(
        onClick = { showDialog = true },
        modifier = modifier.size(20.dp)
    ) {
        Icon(
            imageVector = Icons.Default.Info,
            contentDescription = "اطلاعات",
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(16.dp)
        )
    }

    if (showDialog) {
        AlertDialog(
            containerColor = MaterialTheme.colorScheme.surface,
            onDismissRequest = { showDialog = false },
            title = {
                Text(
                    text = "اطلاعات",
                    style = MaterialTheme.typography.titleMedium
                )
            },
            text = {
                Text(
                    text = infoText,
                    style = MaterialTheme.typography.bodyMedium
                )
            },
            confirmButton = {
                TextButton(
                    onClick = { showDialog = false }
                ) {
                    Text("متوجه شدم")
                }
            }
        )
    }
}

/**
 * دریافت متن توضیحی برای نوع تست
 */
private fun getTestTypeDescription(testType: CpuTestType): String {
    return when (testType) {
        CpuTestType.SINGLE_CORE -> """
            تست تک هسته‌ای:
            • تنها یک هسته پردازنده فعال می‌شود
            • انتخاب تعداد هسته غیرفعال می‌شود
            • محاسبات پیچیده ریاضی برای یک هسته
            • مناسب برای تست عملکرد تک رشته‌ای
            • مصرف انرژی کم و دمای کنترل‌شده
            • ارزیابی کیفیت و سرعت یک هسته
        """.trimIndent()
        
        CpuTestType.MULTI_CORE -> """
            تست چند هسته‌ای:
            • هر هسته نوع محاسبات متفاوتی انجام می‌دهد
            • امکان انتخاب تعداد هسته‌های فعال
            • هسته 1: محاسبات مثلثاتی، هسته 2: لگاریتمی، ...
            • تست واقعی توزیع بار بین هسته‌ها
            • شناسایی عدم تعادل بین هسته‌ها
            • مناسب برای کاربردهای چندنخی
        """.trimIndent()
        
        CpuTestType.STABILITY -> """
            تست پایداری:
            • الگوهای متغیر بار در طول زمان (20%-100%)
            • 16 فاز مختلف شدت در چرخه‌های زمانی
            • شبیه‌سازی استفاده واقعی سیستم
            • تشخیص throttling و ناپایداری حرارتی
            • تست قابلیت تطبیق با تغییرات ناگهانی
            • مناسب برای بررسی مدیریت انرژی
        """.trimIndent()
        
        CpuTestType.PEAK_PERFORMANCE -> """
            تست حداکثر عملکرد:
            • شدت اجباری روی حداکثر (نابل تغییر)
            • ترکیب همه نوع محاسبات سنگین همزمان
            • محاسبات ماتریسی، فیبوناچی، مثلثاتی
            • بدون توقف و با حداکثر فشار مداوم
            • یافتن حد بالایی عملکرد سخت‌افزار
            ⚠️ خطر بالا - نظارت مداوم ضروری
        """.trimIndent()
    }
}

/**
 * دریافت متن توضیحی برای شدت تست
 */
private fun getIntensityDescription(intensity: TestIntensity): String {
    return when (intensity) {
        TestIntensity.LOW -> """
            شدت کم (30% بار):
            • مناسب برای دستگاه‌های ضعیف یا قدیمی
            • دمای پایین و مصرف انرژی کم
            • تست ملایم برای بررسی اولیه
            • خطر کمی برای سخت‌افزار
        """.trimIndent()
        
        TestIntensity.MEDIUM -> """
            شدت متوسط (60% بار):
            • تعادل بین عملکرد و ایمنی
            • مناسب برای اکثر دستگاه‌ها
            • دمای متوسط و مصرف انرژی قابل قبول
            • گزینه پیشنهادی برای تست روزانه
        """.trimIndent()
        
        TestIntensity.HIGH -> """
            شدت بالا (85% بار):
            • تست جدی عملکرد سیستم
            • نیاز به سیستم خنک‌کننده مناسب
            • ممکن است باعث گرم شدن قابل توجه شود
            • مناسب برای دستگاه‌های قدرتمند
        """.trimIndent()
        
        TestIntensity.EXTREME -> """
            شدت شدید (100% بار):
            • حداکثر فشار ممکن بر پردازنده
            • خطر بالای گرم شدن و throttling
            • فقط برای تست‌های تخصصی
            • نیاز به نظارت مداوم و سیستم خنک‌کننده قوی
            ⚠️ هشدار: استفاده با احتیاط!
        """.trimIndent()
    }
}

// توابع کمکی برای رنگ‌ها و متن‌ها

@Composable
private fun getUsageColor(usage: Float): Color {
    return when {
        usage > 90f -> Color.Red
        usage > 70f -> Color(0xFFFFA500) // Orange
        usage > 50f -> MaterialTheme.colorScheme.primary
        else -> MaterialTheme.colorScheme.secondary
    }
}

@Composable
private fun getTemperatureColor(temperature: Double): Color {
    return when {
        temperature > 85.0 -> Color.Red
        temperature > 70.0 -> Color(0xFFFFA500) // Orange
        temperature > 50.0 -> MaterialTheme.colorScheme.primary
        else -> MaterialTheme.colorScheme.secondary
    }
}

@Composable
private fun getScoreColor(score: Int): Color {
    return when {
        score >= 80 -> Color(0xFF4CAF50) // Green
        score >= 60 -> Color(0xFFFFA500) // Orange
        score >= 40 -> MaterialTheme.colorScheme.primary
        else -> Color.Red
    }
}

@Composable
private fun getStatusColor(status: TestStatus): Color {
    return when (status) {
        TestStatus.IDLE -> MaterialTheme.colorScheme.onSurface
        TestStatus.PREPARING -> MaterialTheme.colorScheme.primary
        TestStatus.RUNNING -> Color(0xFF4CAF50) // Green
        TestStatus.STOPPING -> Color(0xFFFFA500) // Orange
        TestStatus.COMPLETED -> Color(0xFF4CAF50) // Green
        TestStatus.ERROR -> Color.Red
    }
}

@Composable
private fun getThermalStatusColor(status: ThermalStatus): Color {
    return when (status) {
        ThermalStatus.NORMAL -> Color(0xFF4CAF50) // Green
        ThermalStatus.WARM -> Color(0xFFFFA500) // Orange
        ThermalStatus.HOT -> Color.Red
        ThermalStatus.CRITICAL -> Color(0xFF8B0000) // Dark Red
        ThermalStatus.UNKNOWN -> MaterialTheme.colorScheme.onSurfaceVariant
    }
}

@Composable
private fun getIntensityColor(intensity: TestIntensity): Color {
    return when (intensity) {
        TestIntensity.LOW -> Color(0xFF4CAF50) // Green
        TestIntensity.MEDIUM -> Color(0xFFFFA500) // Orange
        TestIntensity.HIGH -> Color(0xFFFF5722) // Deep Orange
        TestIntensity.EXTREME -> Color.Red
    }
}

@Composable
private fun getTestTypeText(testType: CpuTestType): String {
    return when (testType) {
        CpuTestType.SINGLE_CORE -> stringResource(R.string.test_type_single_core)
        CpuTestType.MULTI_CORE -> stringResource(R.string.test_type_multi_core)
        CpuTestType.STABILITY -> stringResource(R.string.test_type_stability)
        CpuTestType.PEAK_PERFORMANCE -> stringResource(R.string.test_type_peak_performance)
    }
}

@Composable
private fun getIntensityText(intensity: TestIntensity): String {
    return when (intensity) {
        TestIntensity.LOW -> stringResource(R.string.intensity_low)
        TestIntensity.MEDIUM -> stringResource(R.string.intensity_medium)
        TestIntensity.HIGH -> stringResource(R.string.intensity_high)
        TestIntensity.EXTREME -> stringResource(R.string.intensity_extreme)
    }
}

@Composable
private fun getThermalStatusText(status: ThermalStatus): String {
    return when (status) {
        ThermalStatus.NORMAL -> stringResource(R.string.thermal_status_normal)
        ThermalStatus.WARM -> stringResource(R.string.thermal_status_warm)
        ThermalStatus.HOT -> stringResource(R.string.thermal_status_hot)
        ThermalStatus.CRITICAL -> stringResource(R.string.thermal_status_critical)
        ThermalStatus.UNKNOWN -> stringResource(R.string.thermal_status_unknown)
    }
}

@Composable
private fun getPatternText(pattern: PerformancePattern): String {
    return when (pattern) {
        PerformancePattern.THERMAL_THROTTLING -> stringResource(R.string.pattern_thermal_throttling)
        PerformancePattern.STABLE_PERFORMANCE -> stringResource(R.string.pattern_stable_performance)
        PerformancePattern.HIGH_VARIATION -> stringResource(R.string.pattern_high_variation)
        PerformancePattern.OPTIMAL_PERFORMANCE -> stringResource(R.string.pattern_optimal_performance)
    }
}

/**
 * نمایش شدت ثابت
 */
@Composable
private fun FixedIntensityDisplay(intensity: TestIntensity) {
    Column {
        Text(
            text = "شدت تست:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = getIntensityColor(intensity).copy(alpha = 0.1f)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "شدت تست حداکثر عملکرد:",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = getIntensityText(intensity),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = getIntensityColor(intensity)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "⚠️ این تست به‌طور خودکار با حداکثر شدت اجرا می‌شود",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            fontStyle = FontStyle.Italic
        )
    }
}

/**
 * نمایش تعداد هسته ثابت
 */
@Composable
private fun FixedCoreCountDisplay(coreCount: Int) {
    Column {
        Text(
            text = "تعداد هسته‌های درگیر:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "تست تک هسته‌ای:",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "$coreCount هسته",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "🔒 این تست فقط روی یک هسته اجرا می‌شود",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            fontStyle = FontStyle.Italic
        )
    }
}

/**
 * انتخابگر تعداد هسته‌ها
 */
@Composable
private fun CoreCountSelector(
    targetCores: Int,
    maxCores: Int,
    testType: CpuTestType,
    onCoreCountChange: (Int) -> Unit
) {
    Column {
        Text(
            text = "تعداد هسته‌های درگیر:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        // نمایش انتخاب فعلی
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "انتخاب شده:",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = if (targetCores == -1) "همه هسته‌ها ($maxCores)" else "$targetCores هسته",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // گزینه‌های انتخاب
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            // دکمه همه هسته‌ها
            item {
                FilterChip(
                    onClick = { onCoreCountChange(-1) },
                    label = {
                        Text(
                            text = "همه ($maxCores)",
                            style = MaterialTheme.typography.bodySmall
                        )
                    },
                    selected = targetCores == -1,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = MaterialTheme.colorScheme.primary,
                        selectedLabelColor = MaterialTheme.colorScheme.onPrimary
                    )
                )
            }

            // دکمه‌های تعداد مشخص
            items(maxCores) { index ->
                val coreCount = index + 1
                FilterChip(
                    onClick = { onCoreCountChange(coreCount) },
                    label = {
                        Text(
                            text = "$coreCount",
                            style = MaterialTheme.typography.bodySmall
                        )
                    },
                    selected = targetCores == coreCount,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = MaterialTheme.colorScheme.secondary,
                        selectedLabelColor = MaterialTheme.colorScheme.onSecondary
                    )
                )
            }
        }
    }
}

/**
 * تنظیمات ایمنی
 */
@Composable
private fun SafetySettings(
    maxTemperature: Double,
    autoStop: Boolean,
    monitorTemperature: Boolean,
    onMaxTemperatureChange: (Double) -> Unit,
    onAutoStopChange: (Boolean) -> Unit,
    onMonitorTemperatureChange: (Boolean) -> Unit
) {
    Column {
        Text(
            text = "تنظیمات ایمنی:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        // مانیتورینگ دما
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "مانیتورینگ دما",
                style = MaterialTheme.typography.bodyMedium
            )
            Switch(
                checked = monitorTemperature,
                onCheckedChange = onMonitorTemperatureChange
            )
        }

        if (monitorTemperature) {
            Spacer(modifier = Modifier.height(8.dp))

            // توقف خودکار
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "توقف خودکار",
                    style = MaterialTheme.typography.bodyMedium
                )
                Switch(
                    checked = autoStop,
                    onCheckedChange = onAutoStopChange
                )
            }

            if (autoStop) {
                Spacer(modifier = Modifier.height(8.dp))

                // تنظیم حداکثر دما
                Text(
                    text = "حداکثر دما: ${maxTemperature.toInt()}°C",
                    style = MaterialTheme.typography.bodyMedium
                )

                Slider(
                    colors = SliderDefaults.colors(
                        thumbColor = MaterialTheme.colorScheme.primary,
                        activeTrackColor = MaterialTheme.colorScheme.primary,
                        inactiveTrackColor = MaterialTheme.colorScheme.background
                    ),
                    value = maxTemperature.toFloat(),
                    onValueChange = { onMaxTemperatureChange(it.toDouble()) },
                    valueRange = 70f..95f,
                    steps = 4, // 70, 75, 80, 85, 90, 95
                    modifier = Modifier.fillMaxWidth()
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("70°C", style = MaterialTheme.typography.bodySmall)
                    Text("95°C", style = MaterialTheme.typography.bodySmall)
                }
            }
        }
    }
}

/**
 * زمان‌سنج تست
 */
@Composable
private fun TestTimer(
    testConfig: CpuStressTestConfig,
    testStartTime: Long,
    currentTime: Long
) {
    val elapsedTime = if (testStartTime > 0) currentTime - testStartTime else 0L
    val remainingTime = (testConfig.duration - elapsedTime).coerceAtLeast(0L)
    val progress = if (testConfig.duration > 0) elapsedTime.toFloat() / testConfig.duration else 0f

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "زمان‌سنج تست",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "گذشته: ${formatTime(elapsedTime)}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = "باقی‌مانده: ${formatTime(remainingTime)}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            LinearProgressIndicator(
                progress = { progress.coerceIn(0f, 1f) },
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary,
                trackColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "${(progress * 100).toInt()}% کامل شده",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * فرمت کردن زمان به دقیقه:ثانیه
 */
private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val minutes = totalSeconds / 60
    val seconds = totalSeconds % 60
    return String.format("%02d:%02d", minutes, seconds)
}

/**
 * محاسبه امتیاز مدیریت حرارت
 */
private fun getThermalManagementScore(averageTemperature: Double): Int {
    return when {
        averageTemperature <= 50.0 -> 100
        averageTemperature <= 60.0 -> 90
        averageTemperature <= 70.0 -> 75
        averageTemperature <= 80.0 -> 60
        averageTemperature <= 85.0 -> 40
        else -> 20
    }.coerceIn(0, 100)
}