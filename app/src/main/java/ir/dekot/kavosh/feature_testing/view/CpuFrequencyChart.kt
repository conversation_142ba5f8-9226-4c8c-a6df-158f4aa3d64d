package ir.dekot.kavosh.feature_testing.view

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.*

/**
 * نمودار زمان-واقعی فرکانس CPU
 * Real-time CPU frequency chart
 */
@Composable
fun CpuFrequencyChart(
    frequencyHistory: List<List<Long>>, // تاریخچه فرکانس هر هسته
    maxFrequencies: List<Long>, // حداکثر فرکانس هر هسته
    modifier: Modifier = Modifier,
    showLegend: Boolean = true,
    animationDuration: Int = 300
) {
    val density = LocalDensity.current
    val coreColors = remember { generateCoreColors(maxFrequencies.size) }
    
    // انیمیشن برای نمودار
    val animatedProgress by animateFloatAsState(
        targetValue = if (frequencyHistory.isNotEmpty()) 1f else 0f,
        animationSpec = tween(animationDuration),
        label = "ChartAnimation"
    )

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // عنوان نمودار
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "نمودار فرکانس هسته‌ها",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                if (frequencyHistory.isNotEmpty()) {
                    Text(
                        text = "${frequencyHistory.size} نمونه",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // نمودار اصلی
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f))
            ) {
                if (frequencyHistory.isNotEmpty() && maxFrequencies.isNotEmpty()) {
                    Canvas(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        drawFrequencyChart(
                            frequencyHistory = frequencyHistory,
                            maxFrequencies = maxFrequencies,
                            coreColors = coreColors,
                            animatedProgress = animatedProgress
                        )
                    }
                } else {
                    // پیام خالی بودن داده
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "در انتظار داده...",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = "تست را شروع کنید",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
            
            // راهنمای رنگ‌ها
            if (showLegend && maxFrequencies.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                ChartLegend(
                    coreColors = coreColors,
                    coreCount = maxFrequencies.size
                )
            }
        }
    }
}

/**
 * رسم نمودار فرکانس
 */
private fun DrawScope.drawFrequencyChart(
    frequencyHistory: List<List<Long>>,
    maxFrequencies: List<Long>,
    coreColors: List<Color>,
    animatedProgress: Float
) {
    val width = size.width
    val height = size.height
    val padding = 40f
    
    val chartWidth = width - padding * 2
    val chartHeight = height - padding * 2
    
    if (frequencyHistory.isEmpty() || maxFrequencies.isEmpty()) return
    
    // محاسبه حداکثر فرکانس کلی برای مقیاس‌بندی
    val globalMaxFreq = maxFrequencies.maxOrNull() ?: 1L
    val dataPoints = frequencyHistory.size
    
    // رسم خطوط شبکه
    drawGridLines(padding, chartWidth, chartHeight, globalMaxFreq)
    
    // رسم خط هر هسته
    maxFrequencies.forEachIndexed { coreIndex, maxFreq ->
        if (coreIndex < coreColors.size) {
            val coreFrequencies = frequencyHistory.map { 
                it.getOrElse(coreIndex) { 0L }
            }
            
            drawCoreLine(
                frequencies = coreFrequencies,
                maxFreq = globalMaxFreq,
                color = coreColors[coreIndex],
                padding = padding,
                chartWidth = chartWidth,
                chartHeight = chartHeight,
                animatedProgress = animatedProgress
            )
        }
    }
    
    // رسم برچسب‌های محورها
    drawAxisLabels(padding, chartWidth, chartHeight, globalMaxFreq, dataPoints)
}

/**
 * رسم خطوط شبکه
 */
private fun DrawScope.drawGridLines(
    padding: Float,
    chartWidth: Float,
    chartHeight: Float,
    maxFreq: Long
) {
    val gridColor = Color.Gray.copy(alpha = 0.3f)
    val strokeWidth = 1.dp.toPx()
    
    // خطوط افقی (فرکانس)
    for (i in 0..4) {
        val y = padding + (chartHeight * i / 4)
        drawLine(
            color = gridColor,
            start = Offset(padding, y),
            end = Offset(padding + chartWidth, y),
            strokeWidth = strokeWidth
        )
    }
    
    // خطوط عمودی (زمان)
    for (i in 0..4) {
        val x = padding + (chartWidth * i / 4)
        drawLine(
            color = gridColor,
            start = Offset(x, padding),
            end = Offset(x, padding + chartHeight),
            strokeWidth = strokeWidth
        )
    }
}

/**
 * رسم خط یک هسته
 */
private fun DrawScope.drawCoreLine(
    frequencies: List<Long>,
    maxFreq: Long,
    color: Color,
    padding: Float,
    chartWidth: Float,
    chartHeight: Float,
    animatedProgress: Float
) {
    if (frequencies.size < 2) return
    
    val path = Path()
    val strokeWidth = 3.dp.toPx()
    
    // محاسبه نقاط
    val points = frequencies.mapIndexed { index, freq ->
        val x = padding + (chartWidth * index / (frequencies.size - 1).coerceAtLeast(1))
        val normalizedFreq = if (maxFreq > 0) freq.toFloat() / maxFreq else 0f
        val y = padding + chartHeight - (normalizedFreq * chartHeight)
        Offset(x, y)
    }
    
    // اعمال انیمیشن
    val animatedPointCount = (points.size * animatedProgress).toInt().coerceAtLeast(1)
    val animatedPoints = points.take(animatedPointCount)
    
    if (animatedPoints.isNotEmpty()) {
        // شروع مسیر
        path.moveTo(animatedPoints[0].x, animatedPoints[0].y)
        
        // رسم خط صاف بین نقاط
        for (i in 1 until animatedPoints.size) {
            path.lineTo(animatedPoints[i].x, animatedPoints[i].y)
        }
        
        // رسم خط اصلی
        drawPath(
            path = path,
            color = color,
            style = Stroke(
                width = strokeWidth,
                cap = StrokeCap.Round,
                join = StrokeJoin.Round
            )
        )
        
        // حذف رسم نقاط برای ظاهر خطی ساده
    }
}

/**
 * رسم برچسب‌های محورها
 */
private fun DrawScope.drawAxisLabels(
    padding: Float,
    chartWidth: Float,
    chartHeight: Float,
    maxFreq: Long,
    dataPoints: Int
) {
    val textPaint = android.graphics.Paint().apply {
        color = Color.Gray.toArgb()
        textSize = 12.sp.toPx()
        isAntiAlias = true
    }
    
    // برچسب‌های فرکانس (محور Y)
    for (i in 0..4) {
        val freq = (maxFreq * (4 - i) / 4 / 1000).toInt() // تبدیل به MHz
        val y = padding + (chartHeight * i / 4)
        
        drawContext.canvas.nativeCanvas.drawText(
            "${freq}MHz",
            padding - 35.dp.toPx(),
            y + 4.dp.toPx(),
            textPaint
        )
    }
    
    // برچسب‌های زمان (محور X)
    for (i in 0..4) {
        val timeLabel = "${i * dataPoints / 4}s"
        val x = padding + (chartWidth * i / 4)
        
        drawContext.canvas.nativeCanvas.drawText(
            timeLabel,
            x - 10.dp.toPx(),
            padding + chartHeight + 20.dp.toPx(),
            textPaint
        )
    }
}

/**
 * راهنمای رنگ‌ها
 */
@Composable
private fun ChartLegend(
    coreColors: List<Color>,
    coreCount: Int
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier.fillMaxWidth()
    ) {
        items(coreCount) { coreIndex ->
            if (coreIndex < coreColors.size) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = coreColors[coreIndex],
                                shape = RoundedCornerShape(2.dp)
                            )
                    )
                    Text(
                        text = "هسته $coreIndex",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * تولید رنگ‌های متمایز برای هسته‌ها
 */
private fun generateCoreColors(coreCount: Int): List<Color> {
    val baseColors = listOf(
        Color(0xFF2196F3), // Blue
        Color(0xFF4CAF50), // Green
        Color(0xFFFF9800), // Orange
        Color(0xFF9C27B0), // Purple
        Color(0xFFF44336), // Red
        Color(0xFF00BCD4), // Cyan
        Color(0xFFFFEB3B), // Yellow
        Color(0xFF795548)  // Brown
    )
    
    return (0 until coreCount).map { index ->
        baseColors[index % baseColors.size]
    }
}
