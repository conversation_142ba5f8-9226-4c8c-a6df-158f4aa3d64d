package ir.dekot.kavosh.feature_testing.model

import kotlinx.serialization.Serializable

/**
 * تنظیمات تست استرس CPU
 * CPU stress test configuration
 */
@Serializable
data class CpuStressTestConfig(
    val testType: CpuTestType = CpuTestType.MULTI_CORE,
    val duration: Long = 60000, // 1 دقیقه به میلی‌ثانیه
    val targetCores: Int = -1, // -1 برای همه هسته‌ها
    val intensity: TestIntensity = TestIntensity.HIGH,
    val monitorTemperature: Boolean = true,
    val autoStop: Boolean = true, // توقف خودکار در صورت گرم شدن زیاد
    val maxTemperature: Double = 85.0 // حداکثر دمای مجاز (درجه سانتیگراد)
)

/**
 * انواع تست CPU
 * CPU test types
 */
@Serializable
enum class CpuTestType {
    SINGLE_CORE,        // تست تک هسته
    MULTI_CORE,         // تست چند هسته
    STABILITY,          // تست پایداری
    PEAK_PERFORMANCE    // تست حداکثر عملکرد
}

/**
 * شدت تست
 * Test intensity levels
 */
@Serializable
enum class TestIntensity {
    LOW,        // کم
    MEDIUM,     // متوسط
    HIGH,       // بالا
    EXTREME     // شدید
}

/**
 * نتیجه تست استرس CPU
 * CPU stress test result
 */
@Serializable
data class CpuStressTestResult(
    val testId: String,
    val timestamp: Long = System.currentTimeMillis(),
    val testDuration: Long, // مدت زمان تست به میلی‌ثانیه
    val testType: CpuTestType,
    val testIntensity: TestIntensity,
    val coreCount: Int,
    val averageFrequency: Double,
    val maxFrequency: Double,
    val minFrequency: Double,
    val averageTemperature: Double,
    val maxTemperature: Double,
    val minTemperature: Double,
    val averageCpuUsage: Float,
    val maxCpuUsage: Float,
    val performanceScore: Int, // امتیاز از 0 تا 100
    val stabilityScore: Int, // امتیاز پایداری
    val averagePowerConsumption: Double,
    val maxPowerConsumption: Double,
    val throttlingDetected: Boolean,
    val emergencyStopTriggered: Boolean,
    val patterns: List<PerformancePattern> = emptyList(),
    val frequencyHistory: List<FrequencyDataPoint> = emptyList(),
    val temperatureHistory: List<TemperatureDataPoint> = emptyList(),
    val cpuUsageHistory: List<CpuUsageDataPoint> = emptyList(),
    val powerHistory: List<PowerDataPoint> = emptyList()
)

/**
 * نقطه داده فرکانس
 * Frequency data point
 */
@Serializable
data class FrequencyDataPoint(
    val timestamp: Long,
    val coreFrequencies: List<Long>, // فرکانس هر هسته به KHz
    val averageFrequency: Double
)

/**
 * نقطه داده دما
 * Temperature data point
 */
@Serializable
data class TemperatureDataPoint(
    val timestamp: Long,
    val temperature: Double // درجه سانتیگراد
)

/**
 * نقطه داده استفاده CPU
 * CPU usage data point
 */
@Serializable
data class CpuUsageDataPoint(
    val timestamp: Long,
    val overallUsage: Float, // درصد استفاده کلی
    val perCoreUsage: List<Float> // درصد استفاده هر هسته
)

/**
 * نقطه داده مصرف انرژی
 * Power consumption data point
 */
@Serializable
data class PowerDataPoint(
    val timestamp: Long,
    val powerConsumption: Double // وات
)

/**
 * پیش‌تنظیمات تست
 * Test presets
 */
object CpuTestPresets {
    
    /**
     * تست سریع (30 ثانیه، شدت متوسط)
     */
    val QUICK_TEST = CpuStressTestConfig(
        testType = CpuTestType.MULTI_CORE,
        duration = 30000,
        intensity = TestIntensity.MEDIUM,
        maxTemperature = 80.0
    )
    
    /**
     * تست استاندارد (1 دقیقه، شدت بالا)
     */
    val STANDARD_TEST = CpuStressTestConfig(
        testType = CpuTestType.MULTI_CORE,
        duration = 60000,
        intensity = TestIntensity.HIGH,
        maxTemperature = 85.0
    )
    
    /**
     * تست پایداری (5 دقیقه، شدت متغیر)
     */
    val STABILITY_TEST = CpuStressTestConfig(
        testType = CpuTestType.STABILITY,
        duration = 300000,
        intensity = TestIntensity.HIGH,
        maxTemperature = 85.0
    )
    
    /**
     * تست حداکثر عملکرد (2 دقیقه، شدت شدید)
     */
    val PEAK_PERFORMANCE_TEST = CpuStressTestConfig(
        testType = CpuTestType.PEAK_PERFORMANCE,
        duration = 120000,
        intensity = TestIntensity.EXTREME,
        maxTemperature = 90.0
    )
    
    /**
     * تست تک هسته (1 دقیقه)
     */
    val SINGLE_CORE_TEST = CpuStressTestConfig(
        testType = CpuTestType.SINGLE_CORE,
        duration = 60000,
        targetCores = 1,
        intensity = TestIntensity.HIGH,
        maxTemperature = 85.0
    )
    
    /**
     * تست محافظه‌کارانه (کم شدت، دمای پایین)
     */
    val CONSERVATIVE_TEST = CpuStressTestConfig(
        testType = CpuTestType.MULTI_CORE,
        duration = 60000,
        intensity = TestIntensity.LOW,
        maxTemperature = 75.0
    )
    
    /**
     * دریافت لیست همه پیش‌تنظیمات
     */
    fun getAllPresets(): List<Pair<String, CpuStressTestConfig>> {
        return listOf(
            "تست سریع" to QUICK_TEST,
            "تست استاندارد" to STANDARD_TEST,
            "تست پایداری" to STABILITY_TEST,
            "حداکثر عملکرد" to PEAK_PERFORMANCE_TEST,
            "تک هسته" to SINGLE_CORE_TEST,
            "محافظه‌کارانه" to CONSERVATIVE_TEST
        )
    }
}
