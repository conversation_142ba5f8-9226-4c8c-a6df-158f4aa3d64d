package ir.dekot.kavosh.feature_testing.view

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import ir.dekot.kavosh.feature_testing.model.*
import ir.dekot.kavosh.feature_testing.viewModel.TestStatus

/**
 * مانیتور زنده تست - نمایش وضعیت فعلی تست
 * Live test monitor - displays current test status
 */
@Composable
fun LiveTestMonitor(
    realTimeData: RealTimeData,
    testStatus: TestStatus,
    isVisible: Boolean,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { -it },
            animationSpec = tween(500, easing = EaseOutCubic)
        ) + fadeIn(),
        exit = slideOutVertically(
            targetOffsetY = { -it },
            animationSpec = tween(300)
        ) + fadeOut(),
        modifier = modifier
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // عنوان با نشانگر وضعیت
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        StatusIndicator(testStatus = testStatus)
                        Text(
                            text = "مانیتور زنده",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                    
                    // آیکون وضعیت
                    Icon(
                        imageVector = getStatusIcon(testStatus),
                        contentDescription = null,
                        tint = getStatusColor(testStatus),
                        modifier = Modifier.size(24.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // متریک‌های کلیدی
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    LiveMetric(
                        label = "CPU",
                        value = "${realTimeData.cpuUsage.toInt()}%",
                        color = getUsageColor(realTimeData.cpuUsage),
                        icon = Icons.Default.Memory
                    )
                    
                    LiveMetric(
                        label = "دما",
                        value = "${realTimeData.temperature.toInt()}°C",
                        color = getTemperatureColor(realTimeData.temperature),
                        icon = Icons.Default.Thermostat
                    )
                    
                    LiveMetric(
                        label = "امتیاز",
                        value = "${realTimeData.performanceScore}",
                        color = getScoreColor(realTimeData.performanceScore),
                        icon = Icons.Default.Speed
                    )
                    
                    if (realTimeData.powerConsumption > 0) {
                        LiveMetric(
                            label = "انرژی",
                            value = "${"%.1f".format(realTimeData.powerConsumption)}W",
                            color = MaterialTheme.colorScheme.secondary,
                            icon = Icons.Default.BatteryChargingFull
                        )
                    }
                }
                
                // هشدارها
                if (realTimeData.isThrottling || realTimeData.thermalStatus == ThermalStatus.HOT) {
                    Spacer(modifier = Modifier.height(8.dp))
                    WarningSection(realTimeData = realTimeData)
                }
            }
        }
    }
}

/**
 * نشانگر وضعیت
 */
@Composable
private fun StatusIndicator(testStatus: TestStatus) {
    val color = getStatusColor(testStatus)
    val isActive = testStatus == TestStatus.RUNNING
    
    // انیمیشن پالس برای وضعیت فعال
    val infiniteTransition = rememberInfiniteTransition(label = "StatusPulse")
    val alpha by infiniteTransition.animateFloat(
        initialValue = if (isActive) 0.3f else 1f,
        targetValue = if (isActive) 1f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "AlphaPulse"
    )
    
    Box(
        modifier = Modifier
            .size(12.dp)
            .clip(CircleShape)
            .background(color.copy(alpha = alpha))
    )
}

/**
 * متریک زنده
 */
@Composable
private fun LiveMetric(
    label: String,
    value: String,
    color: Color,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(20.dp)
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = color
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
        )
    }
}

/**
 * بخش هشدارها
 */
@Composable
private fun WarningSection(realTimeData: RealTimeData) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFEBEE) // قرمز روشن
        ),
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = Color(0xFFD32F2F), // قرمز
                modifier = Modifier.size(20.dp)
            )
            
            Column {
                if (realTimeData.isThrottling) {
                    Text(
                        text = "کاهش عملکرد تشخیص داده شد",
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFFD32F2F)
                    )
                }
                
                if (realTimeData.thermalStatus == ThermalStatus.HOT) {
                    Text(
                        text = "دمای بالا - ${realTimeData.temperature.toInt()}°C",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFFD32F2F)
                    )
                }
            }
        }
    }
}

// توابع کمکی

@Composable
private fun getStatusColor(status: TestStatus): Color {
    return when (status) {
        TestStatus.IDLE -> MaterialTheme.colorScheme.outline
        TestStatus.PREPARING -> MaterialTheme.colorScheme.primary
        TestStatus.RUNNING -> Color(0xFF4CAF50) // سبز
        TestStatus.STOPPING -> Color(0xFFFF9800) // نارنجی
        TestStatus.COMPLETED -> Color(0xFF4CAF50) // سبز
        TestStatus.ERROR -> Color(0xFFF44336) // قرمز
    }
}

private fun getStatusIcon(status: TestStatus): androidx.compose.ui.graphics.vector.ImageVector {
    return when (status) {
        TestStatus.IDLE -> Icons.Default.PlayArrow
        TestStatus.PREPARING -> Icons.Default.Refresh
        TestStatus.RUNNING -> Icons.Default.PlayArrow
        TestStatus.STOPPING -> Icons.Default.Stop
        TestStatus.COMPLETED -> Icons.Default.CheckCircle
        TestStatus.ERROR -> Icons.Default.Error
    }
}

@Composable
private fun getUsageColor(usage: Float): Color {
    return when {
        usage > 90f -> Color(0xFFF44336) // قرمز
        usage > 70f -> Color(0xFFFF9800) // نارنجی
        usage > 50f -> Color(0xFF4CAF50) // سبز
        else -> MaterialTheme.colorScheme.primary
    }
}

@Composable
private fun getTemperatureColor(temperature: Double): Color {
    return when {
        temperature > 85.0 -> Color(0xFFF44336) // قرمز
        temperature > 70.0 -> Color(0xFFFF9800) // نارنجی
        temperature > 50.0 -> Color(0xFF4CAF50) // سبز
        else -> MaterialTheme.colorScheme.primary
    }
}

@Composable
private fun getScoreColor(score: Int): Color {
    return when {
        score >= 80 -> Color(0xFF4CAF50) // سبز
        score >= 60 -> Color(0xFFFF9800) // نارنجی
        score >= 40 -> MaterialTheme.colorScheme.primary
        else -> Color(0xFFF44336) // قرمز
    }
}

/**
 * نمایش پیش‌نمایش کوچک نمودارها
 */
@Composable
fun MiniChartPreview(
    frequencyHistory: List<List<Long>>,
    temperatureHistory: List<Double>,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // پیش‌نمایش نمودار فرکانس
        Card(
            modifier = Modifier.weight(1f),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            )
        ) {
            Column(
                modifier = Modifier.padding(8.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "فرکانس",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "${frequencyHistory.size} نمونه",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium
                )
            }
        }
        
        // پیش‌نمایش نمودار دما
        Card(
            modifier = Modifier.weight(1f),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            )
        ) {
            Column(
                modifier = Modifier.padding(8.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "دما",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                if (temperatureHistory.isNotEmpty()) {
                    Text(
                        text = "${temperatureHistory.last().toInt()}°C",
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium,
                        color = getTemperatureColor(temperatureHistory.last())
                    )
                }
            }
        }
    }
}
