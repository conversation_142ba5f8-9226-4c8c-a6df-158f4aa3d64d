package ir.dekot.kavosh.feature_testing.viewModel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import ir.dekot.kavosh.feature_deviceInfo.model.CpuInfo
import ir.dekot.kavosh.feature_deviceInfo.model.repository.HardwareRepository
import ir.dekot.kavosh.feature_testing.CpuStresser
import ir.dekot.kavosh.feature_testing.model.CpuStressTestConfig
import ir.dekot.kavosh.feature_testing.model.RealTimeDataCollector
import ir.dekot.kavosh.feature_testing.model.PerformanceMetrics
import ir.dekot.kavosh.feature_testing.model.RealTimeData
import ir.dekot.kavosh.feature_testing.model.PerformanceStatistics
import ir.dekot.kavosh.feature_testing.model.ChartData
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CpuStressTestViewModel @Inject constructor(
    private val hardwareRepository: HardwareRepository,
    private val realTimeDataCollector: RealTimeDataCollector,
    private val performanceMetrics: PerformanceMetrics
) : ViewModel() {

    private val cpuStresser = CpuStresser()

    // وضعیت تست (روشن یا خاموش)
    private val _isTesting = MutableStateFlow(false)
    val isTesting: StateFlow<Boolean> = _isTesting.asStateFlow()

    // اطلاعات CPU که تغییر نمی‌کنند
    private val _cpuInfo = MutableStateFlow(CpuInfo())
    val cpuInfo: StateFlow<CpuInfo> = _cpuInfo.asStateFlow()

    // فرکانس لحظه‌ای هسته‌ها (برای سازگاری با کد قبلی)
    private val _liveFrequencies = MutableStateFlow<List<String>>(emptyList())
    val liveFrequencies: StateFlow<List<String>> = _liveFrequencies.asStateFlow()

    // داده‌های زنده جدید
    val realTimeData: StateFlow<RealTimeData> = realTimeDataCollector.realTimeData

    // تنظیمات تست
    private val _testConfig = MutableStateFlow(CpuStressTestConfig())
    val testConfig: StateFlow<CpuStressTestConfig> = _testConfig.asStateFlow()

    // آمار عملکرد
    private val _performanceStats = MutableStateFlow<PerformanceStatistics?>(null)
    val performanceStats: StateFlow<PerformanceStatistics?> = _performanceStats.asStateFlow()

    // وضعیت تست
    private val _testStatus = MutableStateFlow(TestStatus.IDLE)
    val testStatus: StateFlow<TestStatus> = _testStatus.asStateFlow()

    // پیام وضعیت
    private val _statusMessage = MutableStateFlow("")
    val statusMessage: StateFlow<String> = _statusMessage.asStateFlow()

    private var pollingJob: Job? = null
    private var testStartTime: Long = 0L

    init {
        viewModelScope.launch {
            _cpuInfo.value = hardwareRepository.getCpuInfo()
        }

        // مانیتورینگ داده‌های زنده برای به‌روزرسانی فرکانس‌ها (سازگاری)
        viewModelScope.launch {
            realTimeDataCollector.realTimeData.collect { data ->
                // فقط فرکانس هسته‌های فعال را نمایش می‌دهد
                _liveFrequencies.value = data.frequencies.map { freq ->
                    "${freq / 1000} MHz"
                }
            }
        }
    }

    /**
     * رویداد کلیک روی دکمه شروع/پایان تست را مدیریت می‌کند.
     */
    fun onTestToggle() {
        _isTesting.value = !_isTesting.value
        if (_isTesting.value) {
            startTest()
        } else {
            stopTest()
        }
    }

    /**
     * تغییر تنظیمات تست
     */
    fun updateTestConfig(config: CpuStressTestConfig) {
        if (!_isTesting.value) {
            _testConfig.value = config
        }
    }

    /**
     * شروع تست با تنظیمات مشخص
     */
    fun startTestWithConfig(config: CpuStressTestConfig) {
        if (!_isTesting.value) {
            _testConfig.value = config
            onTestToggle()
        }
    }

    private fun startTest() {
        testStartTime = System.currentTimeMillis()
        _testStatus.value = TestStatus.PREPARING
        _statusMessage.value = "آماده‌سازی تست..."

        viewModelScope.launch {
            try {
                val config = _testConfig.value
                
                // محاسبه تعداد هسته‌های فعال
                val activeCoreCount = if (config.targetCores == -1) {
                    Runtime.getRuntime().availableProcessors()
                } else {
                    config.targetCores.coerceAtMost(Runtime.getRuntime().availableProcessors())
                }
                
                // پاکسازی داده‌های قبلی
                realTimeDataCollector.clearHistory()
                
                // تنظیم تعداد هسته‌های فعال در مانیتور
                realTimeDataCollector.setActiveCoreCount(activeCoreCount)

                // شروع جمع‌آوری داده‌های زنده با فاصله کمتر
                realTimeDataCollector.startCollection(500L)

                // کمی تأخیر برای آماده‌سازی
                delay(1000)

                _testStatus.value = TestStatus.RUNNING
                _statusMessage.value = "تست در حال اجرا بر روی $activeCoreCount هسته..."

                // شروع پردازش سنگین
                cpuStresser.start(config)

                // حذف مانیتورینگ قدیمی - فقط از realTimeDataCollector استفاده می‌کنیم
                // startPolling() - این خط حذف شده است

                // مانیتورینگ مدت زمان تست
                monitorTestDuration()

            } catch (e: Exception) {
                _testStatus.value = TestStatus.ERROR
                _statusMessage.value = "خطا در شروع تست: ${e.message}"
                stopTest()
            }
        }
    }

    private fun stopTest() {
        _testStatus.value = TestStatus.STOPPING
        _statusMessage.value = "توقف تست..."

        viewModelScope.launch {
            try {
                // توقف پردازش سنگین
                cpuStresser.stop()

                // حذف توقف مانیتورینگ قدیمی - فقط از realTimeDataCollector استفاده می‌کنیم
                // stopPolling() - این خط حذف شده است

                // توقف جمع‌آوری داده‌های زنده
                realTimeDataCollector.stopCollection()

                // محاسبه آمار نهایی
                _performanceStats.value = realTimeDataCollector.getPerformanceStatistics()

                _testStatus.value = TestStatus.COMPLETED
                _statusMessage.value = "تست کامل شد"

            } catch (e: Exception) {
                _testStatus.value = TestStatus.ERROR
                _statusMessage.value = "خطا در توقف تست: ${e.message}"
            }
        }
    }

    // حذف شده - مانیتورینگ قدیمی دیگر استفاده نمی‌شود
    // فقط از realTimeDataCollector استفاده می‌کنیم که هسته‌های فعال را رعایت می‌کند

    /**
     * مانیتورینگ مدت زمان تست و توقف خودکار
     */
    private fun monitorTestDuration() {
        viewModelScope.launch {
            val config = _testConfig.value
            val duration = config.duration

            if (duration > 0) {
                delay(duration)
                if (_isTesting.value) {
                    _statusMessage.value = "تست به پایان رسید"
                    onTestToggle() // توقف خودکار
                }
            }

            // مانیتورینگ دمای بحرانی
            if (config.autoStop && config.monitorTemperature) {
                while (_isTesting.value) {
                    val currentTemp = realTimeDataCollector.realTimeData.value.temperature
                    if (currentTemp > config.maxTemperature) {
                        _statusMessage.value = "توقف اضطراری: دمای بالا (${currentTemp.toInt()}°C)"
                        onTestToggle()
                        break
                    }
                    delay(2000) // بررسی هر ۲ ثانیه
                }
            }
        }
    }

    /**
     * دریافت داده‌های نمودار
     */
    fun getChartData(): ChartData {
        return realTimeDataCollector.getChartData()
    }

    /**
     * دریافت زمان شروع تست
     */
    fun getTestStartTime(): Long {
        return testStartTime
    }

    /**
     * ریست کردن آمار
     */
    fun resetStats() {
        if (!_isTesting.value) {
            realTimeDataCollector.clearHistory()
            _performanceStats.value = null
            _statusMessage.value = ""
            _testStatus.value = TestStatus.IDLE
        }
    }

    override fun onCleared() {
        super.onCleared()
        // توقف تست و پاکسازی منابع
        if (_isTesting.value) {
            _isTesting.value = false
            cpuStresser.stop()
        }

        // پاکسازی جمع‌آوری داده‌های زنده
        realTimeDataCollector.cleanup()

        // حذف توقف مانیتورینگ قدیمی - دیگر استفاده نمی‌شود
        // stopPolling() - این خط حذف شده است

        // پاکسازی اضافی
        try {
            cpuStresser.stop()
        } catch (e: Exception) {
            // مدیریت خطای احتمالی در پاکسازی
        }
    }
}

/**
 * وضعیت تست
 */
enum class TestStatus {
    IDLE,        // آماده
    PREPARING,   // آماده‌سازی
    RUNNING,     // در حال اجرا
    STOPPING,    // در حال توقف
    COMPLETED,   // کامل شده
    ERROR        // خطا
}