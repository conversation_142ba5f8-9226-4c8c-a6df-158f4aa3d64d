package ir.dekot.kavosh.feature_testing.view

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.*

/**
 * نمودار عملکرد کلی CPU (نمودار دایره‌ای + آمار)
 * Overall CPU performance chart (circular chart + stats)
 */
@Composable
fun CpuPerformanceChart(
    performanceScore: Int,
    stabilityScore: Int,
    cpuUsage: Float,
    temperature: Double,
    modifier: Modifier = Modifier,
    animationDuration: Int = 1000
) {
    // انیمیشن‌ها
    val animatedPerformance by animateFloatAsState(
        targetValue = performanceScore / 100f,
        animationSpec = tween(animationDuration, easing = EaseOutCubic),
        label = "PerformanceAnimation"
    )
    
    val animatedStability by animateFloatAsState(
        targetValue = stabilityScore / 100f,
        animationSpec = tween(animationDuration + 200, easing = EaseOutCubic),
        label = "StabilityAnimation"
    )
    
    val animatedUsage by animateFloatAsState(
        targetValue = cpuUsage / 100f,
        animationSpec = tween(animationDuration + 400, easing = EaseOutCubic),
        label = "UsageAnimation"
    )

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "عملکرد کلی پردازنده",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // نمودار دایره‌ای عملکرد
                CircularPerformanceIndicator(
                    score = performanceScore,
                    animatedProgress = animatedPerformance,
                    label = "عملکرد",
                    color = getPerformanceColor(performanceScore)
                )
                
                // نمودار دایره‌ای پایداری
                CircularPerformanceIndicator(
                    score = stabilityScore,
                    animatedProgress = animatedStability,
                    label = "پایداری",
                    color = getStabilityColor(stabilityScore)
                )
                
                // نمودار دایره‌ای استفاده
                CircularPerformanceIndicator(
                    score = cpuUsage.toInt(),
                    animatedProgress = animatedUsage,
                    label = "استفاده",
                    color = getUsageColor(cpuUsage),
                    suffix = "%"
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // آمار تفصیلی
            DetailedStats(
                performanceScore = performanceScore,
                stabilityScore = stabilityScore,
                cpuUsage = cpuUsage,
                temperature = temperature
            )
        }
    }
}

/**
 * نمودار دایره‌ای نشانگر عملکرد
 */
@Composable
private fun CircularPerformanceIndicator(
    score: Int,
    animatedProgress: Float,
    label: String,
    color: Color,
    suffix: String = ""
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier.size(80.dp),
            contentAlignment = Alignment.Center
        ) {
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                drawCircularIndicator(
                    progress = animatedProgress,
                    color = color,
                    strokeWidth = 8.dp.toPx()
                )
            }
            
            Text(
                text = "$score$suffix",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = color
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * رسم نمودار دایره‌ای
 */
private fun DrawScope.drawCircularIndicator(
    progress: Float,
    color: Color,
    strokeWidth: Float
) {
    val center = Offset(size.width / 2, size.height / 2)
    val radius = (size.minDimension - strokeWidth) / 2
    
    // رسم دایره پس‌زمینه
    drawCircle(
        color = color.copy(alpha = 0.2f),
        radius = radius,
        center = center,
        style = Stroke(width = strokeWidth)
    )
    
    // رسم قوس پیشرفت
    val sweepAngle = 360f * progress
    drawArc(
        color = color,
        startAngle = -90f,
        sweepAngle = sweepAngle,
        useCenter = false,
        topLeft = Offset(
            center.x - radius,
            center.y - radius
        ),
        size = Size(radius * 2, radius * 2),
        style = Stroke(
            width = strokeWidth,
            cap = StrokeCap.Round
        )
    )
}

/**
 * آمار تفصیلی
 */
@Composable
private fun DetailedStats(
    performanceScore: Int,
    stabilityScore: Int,
    cpuUsage: Float,
    temperature: Double
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "آمار تفصیلی",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatRow("امتیاز کلی:", "${(performanceScore + stabilityScore) / 2}/100")
                StatRow("وضعیت:", getOverallStatus(performanceScore, stabilityScore))
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatRow("استفاده CPU:", "${cpuUsage.toInt()}%")
                StatRow("دما:", "${temperature.toInt()}°C")
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // نوار پیشرفت کلی
            val overallProgress = (performanceScore + stabilityScore + cpuUsage.toInt()) / 300f
            LinearProgressIndicator(
                progress = { overallProgress },
                modifier = Modifier.fillMaxWidth(),
                color = getOverallColor(overallProgress),
                trackColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
            )
        }
    }
}

@Composable
private fun StatRow(label: String, value: String) {
    Column {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

// توابع کمکی برای رنگ‌ها

@Composable
private fun getPerformanceColor(score: Int): Color {
    return when {
        score >= 80 -> Color(0xFF4CAF50) // سبز
        score >= 60 -> Color(0xFFFF9800) // نارنجی
        score >= 40 -> Color(0xFFFFC107) // زرد
        else -> Color(0xFFF44336) // قرمز
    }
}

@Composable
private fun getStabilityColor(score: Int): Color {
    return when {
        score >= 85 -> Color(0xFF2196F3) // آبی
        score >= 70 -> Color(0xFF00BCD4) // فیروزه‌ای
        score >= 50 -> Color(0xFFFF9800) // نارنجی
        else -> Color(0xFFF44336) // قرمز
    }
}

@Composable
private fun getUsageColor(usage: Float): Color {
    return when {
        usage > 90f -> Color(0xFFF44336) // قرمز
        usage > 70f -> Color(0xFFFF9800) // نارنجی
        usage > 50f -> Color(0xFF4CAF50) // سبز
        else -> Color(0xFF9C27B0) // بنفش
    }
}

@Composable
private fun getOverallColor(progress: Float): Color {
    return when {
        progress >= 0.8f -> Color(0xFF4CAF50) // سبز
        progress >= 0.6f -> Color(0xFFFF9800) // نارنجی
        progress >= 0.4f -> Color(0xFFFFC107) // زرد
        else -> Color(0xFFF44336) // قرمز
    }
}

private fun getOverallStatus(performanceScore: Int, stabilityScore: Int): String {
    val avgScore = (performanceScore + stabilityScore) / 2
    return when {
        avgScore >= 80 -> "عالی"
        avgScore >= 60 -> "خوب"
        avgScore >= 40 -> "متوسط"
        else -> "ضعیف"
    }
}

/**
 * نمودار میله‌ای مقایسه‌ای
 */
@Composable
fun CpuComparisonChart(
    currentScores: Map<String, Int>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "مقایسه عملکرد",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            currentScores.forEach { (category, score) ->
                ComparisonBar(
                    label = category,
                    score = score,
                    maxScore = 100
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun ComparisonBar(
    label: String,
    score: Int,
    maxScore: Int
) {
    val progress = score.toFloat() / maxScore
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(800, easing = EaseOutCubic),
        label = "ComparisonAnimation"
    )
    
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = "$score/$maxScore",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = getPerformanceColor(score)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        LinearProgressIndicator(
            progress = { animatedProgress },
            modifier = Modifier.fillMaxWidth(),
            color = getPerformanceColor(score),
            trackColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
        )
    }
}
