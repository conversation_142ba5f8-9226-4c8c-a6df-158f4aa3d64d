package ir.dekot.kavosh.feature_testing.viewModel

import android.os.Build
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import ir.dekot.kavosh.feature_settings.model.SettingsDataSource
import ir.dekot.kavosh.feature_export_and_sharing.model.ExportFormat
import ir.dekot.kavosh.feature_testing.model.DiagnosticDataSource
import ir.dekot.kavosh.feature_testing.model.HealthCheckResult
import ir.dekot.kavosh.feature_testing.model.HealthCheckSummary
import ir.dekot.kavosh.feature_testing.model.HealthStatus
import ir.dekot.kavosh.feature_testing.model.PerformanceScore
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای ابزارهای تشخیصی
 * مدیریت Health Check و Performance Score
 */
@HiltViewModel
class DiagnosticViewModel @Inject constructor(
    private val diagnosticDataSource: DiagnosticDataSource,
    private val settingsDataSource: SettingsDataSource
) : ViewModel() {

    // Health Check
    private val _healthCheckResult = MutableStateFlow<HealthCheckResult?>(null)
    val healthCheckResult: StateFlow<HealthCheckResult?> = _healthCheckResult.asStateFlow()

    private val _isHealthCheckLoading = MutableStateFlow(false)
    val isHealthCheckLoading: StateFlow<Boolean> = _isHealthCheckLoading.asStateFlow()

    // Performance Score
    private val _performanceScore = MutableStateFlow<PerformanceScore?>(null)
    val performanceScore: StateFlow<PerformanceScore?> = _performanceScore.asStateFlow()

    private val _isPerformanceScoreLoading = MutableStateFlow(false)
    val isPerformanceScoreLoading: StateFlow<Boolean> = _isPerformanceScoreLoading.asStateFlow()



    // Error handling
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // Test History
    private val _healthCheckHistory = MutableStateFlow<List<HealthCheckSummary>>(emptyList())
    val healthCheckHistory: StateFlow<List<HealthCheckSummary>> = _healthCheckHistory.asStateFlow()

    private val _performanceScoreHistory = MutableStateFlow<List<PerformanceScore>>(emptyList())
    val performanceScoreHistory: StateFlow<List<PerformanceScore>> = _performanceScoreHistory.asStateFlow()

    // Export Events
    private val _exportRequest = MutableSharedFlow<ExportRequest>()
    val exportRequest: SharedFlow<ExportRequest> = _exportRequest.asSharedFlow()

    init {
        // بارگذاری تاریخچه ذخیره شده هنگام ایجاد ViewModel
        loadSavedHistory()
    }

    /**
     * بارگذاری تاریخچه ذخیره شده از SharedPreferences
     */
    private fun loadSavedHistory() {
        _healthCheckHistory.value = settingsDataSource.getHealthCheckHistory()
        _performanceScoreHistory.value = settingsDataSource.getPerformanceScoreHistory()
    }

    /**
     * انجام بررسی سلامت دستگاه
     */
    fun performHealthCheck() {
        viewModelScope.launch {
            try {
                _isHealthCheckLoading.value = true
                _errorMessage.value = null
                
                val result = diagnosticDataSource.performHealthCheck()
                _healthCheckResult.value = result

                // اضافه کردن به تاریخچه - ایجاد خلاصه کامل
                val summary = HealthCheckSummary(
                    timestamp = result.lastCheckTime,
                    overallScore = result.overallScore,
                    overallStatus = result.overallStatus,
                    deviceName = "${Build.MANUFACTURER} ${Build.MODEL}",
                    androidVersion = Build.VERSION.RELEASE,
                    checks = result.checks,
                    recommendations = result.recommendations,
                    testDuration = System.currentTimeMillis() - result.lastCheckTime,
                    criticalIssuesCount = result.checks.count { it.status == HealthStatus.CRITICAL },
                    warningsCount = result.checks.count { it.status == HealthStatus.POOR || it.status == HealthStatus.FAIR }
                )
                val currentHistory = _healthCheckHistory.value.toMutableList()
                currentHistory.add(0, summary) // اضافه کردن به ابتدای لیست
                if (currentHistory.size > 10) { // نگه داشتن حداکثر 10 تست
                    currentHistory.removeAt(currentHistory.size - 1)
                }
                _healthCheckHistory.value = currentHistory

                // ذخیره در SharedPreferences
                settingsDataSource.saveHealthCheckHistory(currentHistory)
                
            } catch (e: Exception) {
                _errorMessage.value = "Failed to perform health check: ${e.message}"
            } finally {
                _isHealthCheckLoading.value = false
            }
        }
    }

    /**
     * محاسبه امتیاز عملکرد
     */
    fun calculatePerformanceScore() {
        viewModelScope.launch {
            try {
                _isPerformanceScoreLoading.value = true
                _errorMessage.value = null
                
                val result = diagnosticDataSource.calculatePerformanceScore()
                _performanceScore.value = result

                // اضافه کردن به تاریخچه
                val currentHistory = _performanceScoreHistory.value.toMutableList()
                currentHistory.add(0, result)
                if (currentHistory.size > 10) {
                    currentHistory.removeAt(currentHistory.size - 1)
                }
                _performanceScoreHistory.value = currentHistory

                // ذخیره در SharedPreferences
                settingsDataSource.savePerformanceScoreHistory(currentHistory)
                
            } catch (e: Exception) {
                _errorMessage.value = "Failed to calculate performance score: ${e.message}"
            } finally {
                _isPerformanceScoreLoading.value = false
            }
        }
    }



    /**
     * پاک کردن پیام خطا
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * تازه‌سازی همه داده‌ها
     */
    fun refreshAll() {
        performHealthCheck()
        calculatePerformanceScore()
    }

    /**
     * پاک کردن کش داده‌ها
     */
    fun clearCache() {
        _healthCheckResult.value = null
        _performanceScore.value = null
        _errorMessage.value = null
    }

    /**
     * درخواست خروجی گزارش بررسی سلامت
     */
    fun exportHealthCheckReport(format: ExportFormat) {
        viewModelScope.launch {
            _healthCheckResult.value?.let { result ->
                _exportRequest.emit(ExportRequest.HealthCheck(result, format))
            }
        }
    }

    /**
     * درخواست خروجی گزارش امتیاز عملکرد
     */
    fun exportPerformanceScoreReport(format: ExportFormat) {
        viewModelScope.launch {
            _performanceScore.value?.let { score ->
                _exportRequest.emit(ExportRequest.PerformanceScore(score, format))
            }
        }
    }



    /**
     * درخواست خروجی گزارش تاریخچه بررسی سلامت
     */
    fun exportHealthCheckHistoryReport(summary: HealthCheckSummary, format: ExportFormat) {
        viewModelScope.launch {
            _exportRequest.emit(ExportRequest.HealthCheckHistory(summary, format))
        }
    }

    /**
     * درخواست خروجی گزارش تاریخچه امتیاز عملکرد
     */
    fun exportPerformanceScoreHistoryReport(score: PerformanceScore, format: ExportFormat) {
        viewModelScope.launch {
            _exportRequest.emit(ExportRequest.PerformanceScoreHistory(score, format))
        }
    }



    /**
     * بهینه‌سازی ۲: پاکسازی منابع برای جلوگیری از نشت حافظه
     * Optimization 2: Resource cleanup to prevent memory leaks
     */
    override fun onCleared() {
        super.onCleared()
        // پاکسازی کش داده‌ها
        // Clear data cache
        clearCache()

        // تمام coroutine های viewModelScope به صورت خودکار لغو می‌شوند
        // All viewModelScope coroutines are automatically cancelled
        // اما برای اطمینان بیشتر، وضعیت‌های loading را false می‌کنیم
        // But for extra safety, we set loading states to false
        _isHealthCheckLoading.value = false
        _isPerformanceScoreLoading.value = false
    }
}

/**
 * انواع درخواست خروجی گزارش
 */
sealed class ExportRequest {
    data class HealthCheck(val result: HealthCheckResult, val format: ExportFormat) : ExportRequest()
    data class PerformanceScore(val score: ir.dekot.kavosh.feature_testing.model.PerformanceScore, val format: ExportFormat) : ExportRequest()
    data class HealthCheckHistory(val summary: HealthCheckSummary, val format: ExportFormat) : ExportRequest()
    data class PerformanceScoreHistory(val score: ir.dekot.kavosh.feature_testing.model.PerformanceScore, val format: ExportFormat) : ExportRequest()
}


