// Path: app/src/main/java/ir/dekot/kavosh/ui/screen/detail/infoCards/AppInfoCard.kt
package ir.dekot.kavosh.feature_deviceInfo.view.infoCards

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil.size.Size
import ir.dekot.kavosh.R
import ir.dekot.kavosh.core.util.ImageSizes
import ir.dekot.kavosh.core.util.rememberAppIconPainter
import ir.dekot.kavosh.feature_deviceInfo.model.AppInfo
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun AppInfoCard(info: AppInfo) {
    var isExpanded by remember { mutableStateOf(false) }
    val dateFormatter = remember { SimpleDateFormat("yyyy/MM/dd HH:mm", Locale.getDefault()) }

    // بهینه‌سازی ۶: استفاده از بارگذاری بهینه آیکون برنامه
    // Optimization 6: Use optimized app icon loading
    val iconPainter = rememberAppIconPainter(
        packageName = info.packageName,
        size = ImageSizes.APP_ICON
    )

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .border(
                1.dp,
                if (isExpanded) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.outline
                },
                shape = RoundedCornerShape(20.dp)
            )
            .clickable { isExpanded = !isExpanded },
        colors = CardDefaults.cardColors(
            if (!isExpanded) {
                MaterialTheme.colorScheme.surface
            } else {
                MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
            }
        ),
        shape = CircleShape.copy(CornerSize(25.dp))
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                // بهینه‌سازی ۶: استفاده از painter بهینه شده
                // Optimization 6: Use optimized painter
                Image(
                    painter = iconPainter,
                    contentDescription = info.appName,
                    modifier = Modifier
                        .size(48.dp)
                        .clip(CircleShape.copy(CornerSize(18.dp)))
                )
                Spacer(modifier = Modifier.width(16.dp))
                Column(modifier = Modifier.weight(1f)) {
                    Text(info.appName, style = MaterialTheme.typography.titleMedium)
                    Text(
                        info.packageName,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            AnimatedVisibility(visible = isExpanded) {
                Column {
                    HorizontalDivider(
                        modifier = Modifier.padding(vertical = 12.dp),
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = stringResource(
                            R.string.app_version,
                            info.versionName,
                            info.versionCode
                        ),
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = stringResource(
                            R.string.app_installed_on,
                            dateFormatter.format(Date(info.installTime))
                        ),
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = stringResource(R.string.app_permissions),
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )
                    if (info.permissions.isEmpty()) {
                        Text(
                            text = stringResource(R.string.app_no_permissions),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    } else {
                        info.permissions.sorted().forEach { permission ->
                            Text(
                                text = "• ${permission.substringAfterLast('.')}",
                                style = MaterialTheme.typography.bodySmall,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}