package ir.dekot.kavosh.feature_deviceInfo.view

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import ir.dekot.kavosh.core.ui.shared_components.EmptyStateMessage
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel

@RequiresApi(Build.VERSION_CODES.R)
@Composable
fun ThermalPage(viewModel: DeviceInfoViewModel) {
    val thermalDetails by viewModel.thermalDetails.collectAsState()

    if (thermalDetails.isEmpty()) {
        EmptyStateMessage("Thermal information is not available for this device.")
    } else {
        // کارت واحد شامل تمام اطلاعات حرارتی
        InfoCard("اطلاعات حرارتی") {
            // بخش سنسورهای حرارتی
            InfoSection("سنسورهای حرارتی") {
                thermalDetails.forEach { thermalInfo ->
                    InfoRow(thermalInfo.type, thermalInfo.temperature)
                }
            }
        }
    }
}