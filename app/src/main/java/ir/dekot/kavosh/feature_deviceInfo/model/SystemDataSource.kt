package ir.dekot.kavosh.feature_deviceInfo.model

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorManager
import android.os.Build
import android.util.DisplayMetrics
import androidx.annotation.RequiresApi
import dagger.hilt.android.qualifiers.ApplicationContext
import ir.dekot.kavosh.feature_deviceInfo.model.SystemInfo
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import javax.inject.Inject
import javax.inject.Singleton

@Suppress("DEPRECATION")
@Singleton
class SystemDataSource @Inject constructor(@param:ApplicationContext private val context: Context) {

    @RequiresApi(Build.VERSION_CODES.R)
    fun getDisplayInfo(activity: Activity): DisplayInfo {
        val displayMetrics = DisplayMetrics()
        activity.windowManager.defaultDisplay.getMetrics(displayMetrics)
        val refreshRate = activity.display?.refreshRate ?: 60.0f
        return DisplayInfo(
            resolution = "${displayMetrics.heightPixels}x${displayMetrics.widthPixels}",
            density = "${displayMetrics.densityDpi} dpi",
            refreshRate = "${DecimalFormat("#.##").format(refreshRate)} Hz"
        )
    }

    @SuppressLint("HardwareIds")
    fun getSystemInfo(): SystemInfo {
        android.util.Log.d("SystemDataSource", "getSystemInfo() called")
        android.util.Log.d("SystemDataSource", "Build.MANUFACTURER: ${Build.MANUFACTURER}")
        android.util.Log.d("SystemDataSource", "Build.MODEL: ${Build.MODEL}")
        android.util.Log.d("SystemDataSource", "Build.BRAND: ${Build.BRAND}")

        val systemInfo = SystemInfo(
            // اطلاعات اصلی سیستم
            androidVersion = Build.VERSION.RELEASE,
            sdkLevel = Build.VERSION.SDK_INT.toString(),
            buildNumber = Build.DISPLAY,
            isRooted = isDeviceRooted(),

            // اطلاعات دستگاه
            deviceManufacturer = Build.MANUFACTURER ?: "نامشخص",
            deviceBrand = Build.BRAND ?: "نامشخص",
            deviceModel = Build.MODEL ?: "نامشخص",
            deviceProduct = Build.PRODUCT ?: "نامشخص",
            deviceBoard = Build.BOARD ?: "نامشخص",
            deviceHardware = Build.HARDWARE ?: "نامشخص",

            // اطلاعات بیلد
            buildId = Build.ID ?: "نامشخص",
            buildDisplay = Build.DISPLAY ?: "نامشخص",
            buildFingerprint = Build.FINGERPRINT ?: "نامشخص",
            buildTags = Build.TAGS ?: "نامشخص",
            buildType = Build.TYPE ?: "نامشخص",
            buildUser = Build.USER ?: "نامشخص",
            buildHost = Build.HOST ?: "نامشخص",
            buildTime = formatBuildTime(Build.TIME),

            // اطلاعات امنیتی
            securityPatchLevel = getSecurityPatchLevel(),

            // اطلاعات زبان و منطقه
            systemLanguage = getSystemLanguage(),
            systemCountry = getSystemCountry(),
            timeZone = getTimeZone(),

            // اطلاعات کرنل و Java
            kernelVersion = getKernelVersion().also {
                android.util.Log.d("SystemDataSource", "Kernel version: $it")
            },
            javaVmVersion = getJavaVmVersion(),
            javaVmVendor = getJavaVmVendor(),

            // اطلاعات اضافی
            bootloader = Build.BOOTLOADER ?: "نامشخص",
            radioVersion = getRadioVersion()
        )

        return systemInfo
    }

    fun getSensorInfo(activity: Activity): List<SensorInfo> {
        val sensorManager = activity.getSystemService(Activity.SENSOR_SERVICE) as SensorManager
        return sensorManager.getSensorList(Sensor.TYPE_ALL).map {
            // *** تغییر کلیدی: پاس دادن نوع سنسور به مدل ***
            SensorInfo(name = it.name, vendor = it.vendor, type = it.type)
        }
    }

    private fun isDeviceRooted(): Boolean {
        val paths = arrayOf(
            "/system/app/Superuser.apk", "/sbin/su", "/system/bin/su", "/system/xbin/su",
            "/data/local/xbin/su", "/data/local/bin/su", "/system/sd/xbin/su",
            "/system/bin/failsafe/su", "/data/local/su"
        )
        for (path in paths) {
            if (File(path).exists()) return true
        }
        return false
    }

    /**
     * *** تابع جدید: ***
     * اطلاعات نسخه برنامه را از پکیج منیجر دریافت می‌کند.
     */
    fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            "${packageInfo.versionName} (${packageInfo.versionCode})"
        } catch (_: Exception) {
            "N/A"
        }
    }

    /**
     * دریافت Security Patch Level
     */
    private fun getSecurityPatchLevel(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Build.VERSION.SECURITY_PATCH
            } else {
                "نامشخص"
            }
        } catch (e: Exception) {
            "نامشخص"
        }
    }

    /**
     * دریافت زبان سیستم
     */
    private fun getSystemLanguage(): String {
        return try {
            val locale = Locale.getDefault()
            "${locale.displayLanguage} (${locale.language})"
        } catch (e: Exception) {
            "نامشخص"
        }
    }

    /**
     * دریافت کشور سیستم
     */
    private fun getSystemCountry(): String {
        return try {
            val locale = Locale.getDefault()
            "${locale.displayCountry} (${locale.country})"
        } catch (e: Exception) {
            "نامشخص"
        }
    }

    /**
     * دریافت منطقه زمانی
     */
    private fun getTimeZone(): String {
        return try {
            val timeZone = TimeZone.getDefault()
            "${timeZone.displayName} (${timeZone.id})"
        } catch (e: Exception) {
            "نامشخص"
        }
    }

    /**
     * دریافت نسخه کرنل
     */
    private fun getKernelVersion(): String {
        return try {
            android.util.Log.d("SystemDataSource", "Attempting to get kernel version...")

            // روش اول: خواندن از /proc/version
            val procVersionFile = File("/proc/version")
            android.util.Log.d("SystemDataSource", "/proc/version exists: ${procVersionFile.exists()}, canRead: ${procVersionFile.canRead()}")

            if (procVersionFile.exists() && procVersionFile.canRead()) {
                val content = procVersionFile.readText().trim()
                android.util.Log.d("SystemDataSource", "/proc/version content: $content")

                if (content.isNotEmpty()) {
                    // استخراج نسخه کرنل از متن
                    val versionRegex = Regex("Linux version ([^\\s]+)")
                    val matchResult = versionRegex.find(content)
                    if (matchResult != null) {
                        val version = "Linux ${matchResult.groupValues[1]}"
                        android.util.Log.d("SystemDataSource", "Extracted kernel version: $version")
                        return version
                    }
                    // اگر regex کار نکرد، اولین قسمت را برگردان
                    val simpleVersion = content.split(" ").take(3).joinToString(" ")
                    android.util.Log.d("SystemDataSource", "Simple kernel version: $simpleVersion")
                    return simpleVersion
                }
            }

            // روش دوم: استفاده از System Property
            val kernelVersion = System.getProperty("os.version")
            android.util.Log.d("SystemDataSource", "System property os.version: $kernelVersion")
            if (!kernelVersion.isNullOrEmpty()) {
                return "Linux $kernelVersion"
            }

            // روش سوم: خواندن از /proc/sys/kernel/version
            val kernelVersionFile = File("/proc/sys/kernel/version")
            if (kernelVersionFile.exists() && kernelVersionFile.canRead()) {
                val content = kernelVersionFile.readText().trim()
                android.util.Log.d("SystemDataSource", "/proc/sys/kernel/version content: $content")
                if (content.isNotEmpty()) {
                    return content
                }
            }

            // روش چهارم: استفاده از uname command
            try {
                val process = Runtime.getRuntime().exec("uname -r")
                val reader = BufferedReader(java.io.InputStreamReader(process.inputStream))
                val result = reader.readLine()?.trim()
                reader.close()
                process.waitFor()

                if (!result.isNullOrEmpty()) {
                    android.util.Log.d("SystemDataSource", "uname -r result: $result")
                    return "Linux $result"
                }
            } catch (e: Exception) {
                android.util.Log.e("SystemDataSource", "Error running uname command", e)
            }

            android.util.Log.w("SystemDataSource", "Could not determine kernel version")
            "نامشخص"
        } catch (e: Exception) {
            android.util.Log.e("SystemDataSource", "Error getting kernel version", e)
            "نامشخص"
        }
    }

    /**
     * دریافت نسخه Java VM
     */
    private fun getJavaVmVersion(): String {
        return try {
            System.getProperty("java.vm.version") ?: "نامشخص"
        } catch (e: Exception) {
            "نامشخص"
        }
    }

    /**
     * دریافت سازنده Java VM
     */
    private fun getJavaVmVendor(): String {
        return try {
            System.getProperty("java.vm.vendor") ?: "نامشخص"
        } catch (e: Exception) {
            "نامشخص"
        }
    }

    /**
     * فرمت کردن زمان بیلد
     */
    private fun formatBuildTime(buildTime: Long): String {
        return try {
            val sdf = SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.getDefault())
            sdf.format(Date(buildTime))
        } catch (e: Exception) {
            "نامشخص"
        }
    }

    /**
     * دریافت نسخه رادیو
     */
    private fun getRadioVersion(): String {
        return try {
            Build.getRadioVersion() ?: "نامشخص"
        } catch (e: Exception) {
            "نامشخص"
        }
    }





}