package ir.dekot.kavosh.feature_deviceInfo.model.repository

import ir.dekot.kavosh.feature_deviceInfo.model.AppInfo
import ir.dekot.kavosh.feature_deviceInfo.model.AppsDataSource
import ir.dekot.kavosh.feature_settings.model.SettingsDataSource
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مخزن اطلاعات برنامه‌ها - مسئول مدیریت اطلاعات مربوط به برنامه‌های نصب شده
 * شامل لیست برنامه‌ها، کش برنامه‌ها و شمارش پکیج‌ها
 */
@Singleton
class ApplicationRepository @Inject constructor(
    private val appsDataSource: AppsDataSource,
    private val settingsDataSource: SettingsDataSource
) {

    /**
     * بهینه‌سازی ۳: دریافت لیست برنامه‌های نصب شده با threading بهینه
     * Optimization 3: Get installed apps with optimized threading
     */
    suspend fun getInstalledApps(): List<AppInfo> = appsDataSource.getInstalledApps()

    /**
     * بهینه‌سازی ۳: دریافت اطلاعات برنامه‌ها (نام مستعار) با threading بهینه
     * Optimization 3: Get apps info (alias) with optimized threading
     */
    suspend fun getAppsInfo(): List<AppInfo> = appsDataSource.getInstalledApps()

    /**
     * بهینه‌سازی ۳: نسخه همزمان برای سازگاری با کد موجود
     * Optimization 3: Synchronous version for compatibility with existing code
     */
    fun getInstalledAppsSync(): List<AppInfo> = appsDataSource.getInstalledAppsSync()

    /**
     * بهینه‌سازی ۳: نسخه همزمان برای سازگاری با کد موجود (نام مستعار)
     * Optimization 3: Synchronous version for compatibility (alias)
     */
    fun getAppsInfoSync(): List<AppInfo> = appsDataSource.getInstalledAppsSync()

    /**
     * دریافت تعداد پکیج‌های فعلی
     * @return تعداد کل پکیج‌های نصب شده
     */
    fun getCurrentPackageCount(): Int = appsDataSource.getPackageCount()

    // بهینه‌سازی ۴: مدیریت کش برنامه‌ها با TTL
    // Optimization 4: Apps cache management with TTL

    /**
     * بهینه‌سازی ۴: ذخیره کش برنامه‌ها با TTL
     * Optimization 4: Save apps cache with TTL
     */
    fun saveAppsCache(userApps: List<AppInfo>, systemApps: List<AppInfo>, count: Int) =
        settingsDataSource.saveAppsCache(userApps, systemApps, count)

    /**
     * بهینه‌سازی ۴: دریافت کش برنامه‌های کاربری با بررسی TTL
     * Optimization 4: Get user apps cache with TTL validation
     */
    fun getUserAppsCache(): List<AppInfo>? = settingsDataSource.getUserAppsCache()

    /**
     * بهینه‌سازی ۴: دریافت کش برنامه‌های سیستمی با بررسی TTL
     * Optimization 4: Get system apps cache with TTL validation
     */
    fun getSystemAppsCache(): List<AppInfo>? = settingsDataSource.getSystemAppsCache()

    /**
     * بهینه‌سازی ۴: دریافت تعداد پکیج‌های کش شده با بررسی TTL
     * Optimization 4: Get cached package count with TTL validation
     */
    fun getPackageCountCache(): Int = settingsDataSource.getPackageCountCache()

    /**
     * بهینه‌سازی ۴: پاکسازی کش برنامه‌ها
     * Optimization 4: Clear apps cache
     */
    fun clearAppsCache() = settingsDataSource.clearAppsCache()

    /**
     * بهینه‌سازی ۴: بررسی اعتبار کش برنامه‌ها
     * Optimization 4: Check if apps cache is valid
     */
    fun isAppsCacheValid(): Boolean {
        val userApps = getUserAppsCache()
        val systemApps = getSystemAppsCache()
        val packageCount = getPackageCountCache()

        return userApps != null && systemApps != null && packageCount > 0
    }

    /**
     * بهینه‌سازی ۴: بارگذاری هوشمند برنامه‌ها با استفاده از کش
     * Optimization 4: Smart apps loading with cache utilization
     */
    suspend fun getAppsWithSmartCaching(): List<AppInfo> {
        // ابتدا کش را بررسی می‌کنیم
        // First check cache
        val cachedUserApps = getUserAppsCache()
        val cachedSystemApps = getSystemAppsCache()

        if (cachedUserApps != null && cachedSystemApps != null) {
            // کش معتبر است، آن را برمی‌گردانیم
            // Cache is valid, return it
            return cachedUserApps + cachedSystemApps
        }

        // کش معتبر نیست، داده‌های جدید را بارگذاری می‌کنیم
        // Cache is not valid, load fresh data
        val freshApps = getInstalledApps()
        val userApps = freshApps.filter { !it.isSystemApp }
        val systemApps = freshApps.filter { it.isSystemApp }

        // کش جدید را ذخیره می‌کنیم
        // Save new cache
        saveAppsCache(userApps, systemApps, freshApps.size)

        return freshApps
    }
}
