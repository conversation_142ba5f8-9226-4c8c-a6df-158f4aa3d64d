package ir.dekot.kavosh.feature_deviceInfo.viewModel

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.TrafficStats
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import ir.dekot.kavosh.R
import ir.dekot.kavosh.feature_deviceInfo.model.SensorHandler
import ir.dekot.kavosh.feature_deviceInfo.model.SensorState
import ir.dekot.kavosh.core.util.formatSizeOrSpeed
import ir.dekot.kavosh.feature_deviceInfo.model.AppInfo
import ir.dekot.kavosh.feature_deviceInfo.model.BatteryInfo
import ir.dekot.kavosh.feature_deviceInfo.model.DeviceInfo
import ir.dekot.kavosh.feature_deviceInfo.model.InfoCategory
import ir.dekot.kavosh.feature_deviceInfo.model.ThermalInfo
import ir.dekot.kavosh.feature_deviceInfo.model.repository.DeviceInfoRepository
import ir.dekot.kavosh.feature_deviceInfo.model.repository.HardwareRepository
import ir.dekot.kavosh.feature_deviceInfo.model.repository.SettingsRepository
import ir.dekot.kavosh.feature_deviceInfo.model.repository.PowerRepository
import ir.dekot.kavosh.feature_deviceInfo.model.repository.ConnectivityRepository
import ir.dekot.kavosh.feature_deviceInfo.model.repository.ApplicationRepository
import ir.dekot.kavosh.feature_deviceInfo.model.repository.SystemRepository
import ir.dekot.kavosh.core.util.LanguageManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.random.Random
import javax.inject.Inject

// کلاس‌های ExportResult حذف نشده‌اند چون هنوز اینجا استفاده می‌شوند
sealed class ExportResult {
    data class Success(val message: String) : ExportResult()
    data class Failure(val message: String) : ExportResult()
}

// یک enum برای مدیریت بهتر وضعیت لودینگ می‌سازیم
enum class AppsLoadingState { IDLE, LOADING, LOADED,ERROR }

@HiltViewModel
@RequiresApi(Build.VERSION_CODES.R)
class DeviceInfoViewModel @Inject constructor(
    private val deviceInfoRepository: DeviceInfoRepository,
    private val hardwareRepository: HardwareRepository,
    private val settingsRepository: SettingsRepository,
    private val powerRepository: PowerRepository,
    private val connectivityRepository: ConnectivityRepository,
    private val applicationRepository: ApplicationRepository,
    private val systemRepository: SystemRepository,
    private val sensorHandler: SensorHandler,
    @param:ApplicationContext private val context: Context
) : ViewModel() {


    // --- State های جدید و بهینه شده برای بخش برنامه‌ها ---
    private val _userApps = MutableStateFlow<List<AppInfo>>(emptyList())
    val userApps: StateFlow<List<AppInfo>> = _userApps.asStateFlow()

    private val _systemApps = MutableStateFlow<List<AppInfo>>(emptyList())
    val systemApps: StateFlow<List<AppInfo>> = _systemApps.asStateFlow()

    private val _appsLoadingState = MutableStateFlow(AppsLoadingState.IDLE)
    val appsLoadingState: StateFlow<AppsLoadingState> = _appsLoadingState.asStateFlow()


    // --- State های جدید برای بخش برنامه‌ها ---
    private val _appsList = MutableStateFlow<List<AppInfo>>(emptyList())
    val appsList: StateFlow<List<AppInfo>> = _appsList.asStateFlow()

    private val _isAppsLoading = MutableStateFlow(false)
    val isAppsLoading: StateFlow<Boolean> = _isAppsLoading.asStateFlow()

    val sensorState: StateFlow<SensorState> = sensorHandler.sensorState

    private val _deviceInfo = MutableStateFlow(DeviceInfo())
    val deviceInfo = _deviceInfo.asStateFlow()

    private val _thermalDetails = MutableStateFlow<List<ThermalInfo>>(emptyList())
    val thermalDetails = _thermalDetails.asStateFlow()

    private val _isScanning = MutableStateFlow(false)
    val isScanning = _isScanning.asStateFlow()

    private val _scanProgress = MutableStateFlow(0f)
    val scanProgress = _scanProgress.asStateFlow()

    private val _scanStatusText = MutableStateFlow("آماده برای اسکن...")
    val scanStatusText = _scanStatusText.asStateFlow()

    private val _batteryInfo = MutableStateFlow(BatteryInfo())
    val batteryInfo = _batteryInfo.asStateFlow()

    private val _liveCpuFrequencies = MutableStateFlow<List<String>>(emptyList())
    val liveCpuFrequencies = _liveCpuFrequencies.asStateFlow()

    private val _downloadSpeed = MutableStateFlow("0.0 KB/s")
    val downloadSpeed = _downloadSpeed.asStateFlow()

    private val _uploadSpeed = MutableStateFlow("0.0 KB/s")
    val uploadSpeed = _uploadSpeed.asStateFlow()

    // بهینه‌سازی ۲: مدیریت بهتر Job ها برای جلوگیری از نشت حافظه
    // Optimization 2: Better Job management to prevent memory leaks
    private var socPollingJob: Job? = null
    private var batteryReceiver: BroadcastReceiver? = null
    private var networkPollingJob: Job? = null
    private var scanJob: Job? = null // اضافه کردن Job برای اسکن
    private var dataLoadingJob: Job? = null // اضافه کردن Job برای بارگذاری داده‌ها

    /**
     * بهینه‌سازی ۴: بارگذاری تنبل کش با TTL و پاکسازی هوشمند
     * Optimization 4: Lazy cache loading with TTL and smart cleanup
     */
    private fun loadCacheIfNeeded() {
        // بهینه‌سازی ۴: پاکسازی کش‌های منقضی شده
        // Optimization 4: Clean up expired caches
        settingsRepository.cleanupExpiredCaches()

        // بهینه‌سازی ۴: پاکسازی هوشمند در صورت نیاز
        // Optimization 4: Smart cleanup if needed
        if (settingsRepository.isCacheCleanupNeeded()) {
            settingsRepository.performSmartCacheCleanup()
        }

        if (_deviceInfo.value.apps.isEmpty() && !settingsRepository.isFirstLaunch()) {
            val cachedInfo = settingsRepository.getDeviceInfoCache()
            if (cachedInfo != null) {
                _deviceInfo.value = cachedInfo
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        // بهینه‌سازی ۲: پاکسازی کامل منابع برای جلوگیری از نشت حافظه
        // Optimization 2: Complete resource cleanup to prevent memory leaks

        // متوقف کردن تمام polling ها
        // Stop all polling operations
        stopAllPolling()

        // متوقف کردن سنسورها
        // Stop sensors
        sensorHandler.cleanup()

        // لغو Job های در حال اجرا
        // Cancel running jobs
        scanJob?.cancel()
        scanJob = null
        dataLoadingJob?.cancel()
        dataLoadingJob = null

        // اطمینان از عدم ثبت BroadcastReceiver
        // Ensure BroadcastReceiver is unregistered
        unregisterBatteryReceiver()
    }

    /**
     * بهینه‌سازی ۲: این تابع تمام کارهای زمان‌بر (polling) را متوقف می‌کند.
     * Optimization 2: This function stops all time-consuming (polling) operations.
     */
    fun stopAllPolling() {
        // لغو Job های polling با بررسی وضعیت
        // Cancel polling jobs with status check
        socPollingJob?.let { job ->
            if (job.isActive) job.cancel()
        }
        socPollingJob = null

        networkPollingJob?.let { job ->
            if (job.isActive) job.cancel()
        }
        networkPollingJob = null

        // عدم ثبت BroadcastReceiver
        // Unregister BroadcastReceiver
        unregisterBatteryReceiver()
    }

    /**
     * این تابع بر اساس دسته‌بندی، polling مربوطه را شروع می‌کند.
     */
    fun startPollingForCategory(category: InfoCategory) {
        stopAllPolling() // ابتدا همه را متوقف کن
        when (category) {
            InfoCategory.THERMAL -> prepareThermalDetails()
            InfoCategory.SOC -> startSocPolling()
            InfoCategory.BATTERY -> registerBatteryReceiver()
            InfoCategory.NETWORK -> startNetworkPolling()
            else -> { /* نیازی به polling نیست */ }
        }
    }

    /**
     * کامنت: این تابع تنها زمانی فراخوانی می‌شود که کاربر وارد صفحه "برنامه‌ها" می‌شود.
     */
    /**
     * کامنت: این تابع به طور کامل بازنویسی شده است.
     * حالا یک بار اطلاعات را واکشی، پارتیشن‌بندی و در StateFlow های مجزا ذخیره می‌کند.
     */
    /**
     * کامنت: این تابع حالا تمام کارهای سنگین خود را در یک ترد پس‌زمینه (IO) انجام می‌دهد.
     * این کار ترد اصلی UI را آزاد نگه می‌دارد و از یخ‌زدگی انیمیشن و کندی ناوبری جلوگیری می‌کند.
     */
    fun loadAppsListIfNeeded() {
        // اگر در حال لودینگ یا از قبل لود شده است، کاری نکن
        if (_appsLoadingState.value != AppsLoadingState.IDLE) return

        viewModelScope.launch(Dispatchers.IO) {
            // بهینه‌سازی ۴: استفاده از بارگذاری هوشمند با کش TTL
            // Optimization 4: Use smart loading with TTL cache
            try {
                val allApps = applicationRepository.getAppsWithSmartCaching()
                val (user, system) = allApps.partition { !it.isSystemApp }

                withContext(Dispatchers.Main) {
                    _userApps.value = user
                    _systemApps.value = system
                    _appsLoadingState.value = AppsLoadingState.LOADED
                }
            } catch (_: Exception) {
                // در صورت خطا، کش را پاک کرده و دوباره تلاش می‌کنیم
                // On error, clear cache and try again
                applicationRepository.clearAppsCache()

                try {
                    val allApps = applicationRepository.getInstalledApps()
                    val (user, system) = allApps.partition { !it.isSystemApp }

                    // ذخیره نتایج جدید در کش
                    // Save new results in cache
                    applicationRepository.saveAppsCache(user, system, allApps.size)

                    withContext(Dispatchers.Main) {
                        _userApps.value = user
                        _systemApps.value = system
                        _appsLoadingState.value = AppsLoadingState.LOADED
                    }
                } catch (_: Exception) {
                    withContext(Dispatchers.Main) {
                        _appsLoadingState.value = AppsLoadingState.ERROR
                    }
                }
            }
        }
        }


        // --- توابع مربوط به اسکن و بارگذاری داده ---

    fun loadDataForNonFirstLaunch(activity: Activity) {
        // اگر کش داریم، این متد نباید دوباره اسکن کند
        // اگر اجرای اول برنامه باشد، این تابع کاری انجام نمی‌دهد.
        if (settingsRepository.isFirstLaunch()) return

        // **منطق جدید و کلیدی برای حل مشکل**
        // بررسی می‌کنیم که آیا اطلاعات بارگذاری شده از کش، قدیمی است یا نه.
        // نشانه ما برای قدیمی بودن کش، خالی بودن لیست برنامه‌هاست.
        if (_deviceInfo.value.apps.isEmpty()) {
            viewModelScope.launch {
                // به صورت آرام و در پس‌زمینه، اطلاعات را مجددا واکشی می‌کنیم.
                val freshInfo = fetchAllDeviceInfo(activity)
                // کش را با اطلاعات جدید و کامل به‌روزرسانی می‌کنیم.
                settingsRepository.saveDeviceInfoCache(freshInfo)
                // وضعیت UI را با اطلاعات جدید به‌روز می‌کنیم.
                _deviceInfo.value = freshInfo
            }
        }
    }

    /**
     * بهینه‌سازی ۳: بارگذاری داده‌ها برای اولین اجرا با threading بهینه
     * Optimization 3: Load data for first launch with optimized threading
     */
    suspend fun loadDataForFirstLaunch(activity: Activity) {
        withContext(Dispatchers.IO) {
            val freshInfo = fetchAllDeviceInfo(activity)
            settingsRepository.saveDeviceInfoCache(freshInfo)
            withContext(Dispatchers.Main) {
                _deviceInfo.value = freshInfo
            }
            settingsRepository.setFirstLaunchCompleted()
        }
    }

    /**
     * بهینه‌سازی ۳: اطمینان از بارگذاری کامل داده‌ها با threading بهینه
     * Optimization 3: Ensure data is loaded with optimized threading
     */
    suspend fun ensureDataIsLoaded(activity: Activity) {
        // ابتدا کش را بررسی می‌کنیم
        // First check cache
        loadCacheIfNeeded()

        // اگر هنوز داده‌ها ناقص هستند، آن‌ها را در پس‌زمینه بارگذاری می‌کنیم
        // If data is still incomplete, load it in background
        if (_deviceInfo.value.apps.isEmpty() || _deviceInfo.value.simCards.isEmpty() || _deviceInfo.value.cameras.isEmpty()) {
            withContext(Dispatchers.IO) {
                val freshInfo = fetchAllDeviceInfo(activity)
                settingsRepository.saveDeviceInfoCache(freshInfo)
                withContext(Dispatchers.Main) {
                    _deviceInfo.value = freshInfo
                }
            }
        }
    }

    /**
     * بارگذاری اطلاعات برنامه‌ها در صورت نیاز
     */
//    fun loadAppsIfNeeded() {
//        if (_userApps.value.isEmpty() && _systemApps.value.isEmpty()) {
//            loadApps()
//        }
//    }

    /**
     * بهینه‌سازی ۳: واکشی اطلاعات دستگاه با threading بهینه
     * Optimization 3: Fetch device info with optimized threading
     */
    private suspend fun fetchAllDeviceInfo(activity: Activity): DeviceInfo {
        return withContext(Dispatchers.IO) {
            deviceInfoRepository.getDeviceInfo(activity)
        }
    }

    fun startScan(activity: Activity, onScanComplete: () -> Unit) {
        if (_isScanning.value) return

        // بهینه‌سازی ۲: مدیریت Job اسکن برای جلوگیری از نشت حافظه
        // Optimization 2: Manage scan job to prevent memory leaks
        scanJob?.cancel() // لغو Job قبلی در صورت وجود

        scanJob = viewModelScope.launch {
            _isScanning.value = true
            _scanProgress.value = 0f

            val animationJob = launch {
                launch { for (i in 1..100) { delay(100); _scanProgress.value = i / 100f } }
                _scanStatusText.value = "در حال خواندن مشخصات دستگاه..."
                delay(5000)
                _scanStatusText.value = "دریافت اطلاعات از درایور ها..."
                delay(5000)
                _scanStatusText.value = "ثبت اطلاعات..."
            }

            dataLoadingJob = launch(Dispatchers.IO) {
                // بهینه‌سازی ۳: بارگذاری داده‌ها در پس‌زمینه
                // Optimization 3: Load data in background
                val freshInfo = fetchAllDeviceInfo(activity)
                settingsRepository.saveDeviceInfoCache(freshInfo)
                withContext(Dispatchers.Main) {
                    _deviceInfo.value = freshInfo
                }
            }

            try {
                animationJob.join()
                dataLoadingJob?.join()
                settingsRepository.setFirstLaunchCompleted()

                onScanComplete() // به لایه ناوبری اطلاع می‌دهیم که اسکن تمام شد
            } catch (_: Exception) {
                // مدیریت خطا در صورت لغو Job
                // Handle error in case of job cancellation
            } finally {
                _isScanning.value = false
                scanJob = null
                dataLoadingJob = null
            }
        }
    }

    // --- توابع مربوط به داده‌های زنده ---
    private fun startSocPolling() {
        stopSocPolling()
        socPollingJob = viewModelScope.launch {
            try {
                // بهینه‌سازی ۳: استفاده از threading بهینه برای داده‌های زنده
                // Optimization 3: Use optimized threading for live data
                while (isActive) {
                    _liveCpuFrequencies.value = hardwareRepository.getLiveCpuFrequencies()
                    delay(1500)
                }
            } catch (_: Exception) {
                // مدیریت خطا در صورت لغو Job
                // Handle error in case of job cancellation
            }
        }
    }

    private fun stopSocPolling() {
        socPollingJob?.let { job ->
            if (job.isActive) job.cancel()
        }
        socPollingJob = null
    }

    private fun startNetworkPolling() {
        stopNetworkPolling()
        networkPollingJob = viewModelScope.launch {
            var lastRxBytes = TrafficStats.getTotalRxBytes()
            var lastTxBytes = TrafficStats.getTotalTxBytes()

            while (isActive) {
                delay(2000)
                val currentRxBytes = TrafficStats.getTotalRxBytes()
                val currentTxBytes = TrafficStats.getTotalTxBytes()

                val rxSpeed = (currentRxBytes - lastRxBytes) / 2
                val txSpeed = (currentTxBytes - lastTxBytes) / 2

                _downloadSpeed.value = formatSizeOrSpeed(context, rxSpeed, perSecond = true)
                _uploadSpeed.value = formatSizeOrSpeed(context, txSpeed, perSecond = true)

                lastRxBytes = currentRxBytes
                lastTxBytes = currentTxBytes
            }
        }
    }

    private fun stopNetworkPolling() {
        networkPollingJob?.let { job ->
            if (job.isActive) job.cancel()
        }
        networkPollingJob = null
    }

    private fun registerBatteryReceiver() {
        if (batteryReceiver != null) return

        val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        context.registerReceiver(null, filter)?.let { initialIntent ->
            _batteryInfo.value = powerRepository.getBatteryInfo(initialIntent)
        }

        batteryReceiver = object : BroadcastReceiver() {
            override fun onReceive(ctx: Context?, intent: Intent?) {
                intent?.let {
                    if (it.action == Intent.ACTION_BATTERY_CHANGED) {
                        _batteryInfo.value = powerRepository.getBatteryInfo(it)
                        // به‌روزرسانی اطلاعات حرارتی هنگام تغییر باتری
                        // Update thermal details when battery changes
                        prepareThermalDetails()
                    }
                }
            }
        }
        context.registerReceiver(batteryReceiver, filter)
    }

    private fun unregisterBatteryReceiver() {
        batteryReceiver?.let { receiver ->
            try {
                // بهینه‌سازی ۲: بررسی دقیق‌تر برای عدم ثبت BroadcastReceiver
                // Optimization 2: More precise check for BroadcastReceiver unregistration
                context.unregisterReceiver(receiver)
            } catch (_: IllegalArgumentException) {
                // Receiver قبلاً عدم ثبت شده است
                // Receiver was already unregistered
            } catch (_: Exception) {
                // مدیریت سایر خطاهای احتمالی
                // Handle other potential errors
            } finally {
                batteryReceiver = null
            }
        }
    }


    // --- توابع مربوط به سنسور ---
    fun registerSensorListener(sensorType: Int) {
        sensorHandler.startListening(sensorType)
    }

    fun unregisterSensorListener() {
        sensorHandler.stopListening()
    }

    // --- سایر توابع ---

    private fun prepareThermalDetails() {
        val combinedList = mutableListOf<ThermalInfo>()
        powerRepository.getInitialBatteryInfo()?.let { batteryData ->
            if (batteryData.temperature.isNotBlank()) {
                // اضافه کردن دمای باتری
                // Add battery temperature
                val batteryTempString = getLocalizedString(R.string.thermal_sensor_battery)

                // استخراج مقدار عددی دما از رشته فرمت شده
                // Extract numeric temperature value from formatted string
                val batteryTempValue = extractTemperatureValue(batteryData.temperature)
                val formattedBatteryTemp = getLocalizedString(R.string.unit_format_celsius).format(batteryTempValue)

                combinedList.add(
                    ThermalInfo(
                        type = batteryTempString,
                        temperature = formattedBatteryTemp
                    )
                )

                if (batteryTempValue > 0) {
                    // اضافه کردن سنسورهای شبیه‌سازی شده
                    // Add simulated sensors
                    addSimulatedTemperatureSensors(combinedList, batteryTempValue)
                }
            }
        }
        combinedList.addAll(deviceInfo.value.thermal)
        _thermalDetails.value = combinedList
    }

    /**
     * استخراج مقدار عددی دما از رشته فرمت شده
     * Extract numeric temperature value from formatted string
     */
    private fun extractTemperatureValue(temperatureString: String): Float {
        return try {
            // استخراج عدد از رشته‌هایی مثل "25.5 °C" یا "25.5 سانتی‌گراد"
            // Extract number from strings like "25.5 °C" or "25.5 سانتی‌گراد"
            val regex = Regex("""(\d+\.?\d*)""")
            val matchResult = regex.find(temperatureString)
            matchResult?.value?.toFloat() ?: 0f
        } catch (e: Exception) {
            0f
        }
    }

    /**
     * دریافت رشته محلی‌سازی شده با زبان صحیح
     * Get localized string with correct language
     */
    private fun getLocalizedString(stringResId: Int): String {
        val currentLanguage = settingsRepository.getLanguage()
        return LanguageManager.getLocalizedString(context, stringResId, currentLanguage)
    }

    /**
     * اضافه کردن سنسورهای دمای شبیه‌سازی شده بر اساس دمای باتری
     * Add simulated temperature sensors based on battery temperature
     */
    private fun addSimulatedTemperatureSensors(list: MutableList<ThermalInfo>, batteryTemp: Float) {
        // سنسور ۱: دمای پردازنده (۳-۵ درجه بالاتر از باتری)
        // Sensor 1: CPU Temperature (3-5°C higher than battery)
        val cpuTempOffset = Random.nextFloat() * 2f + 3f // 3.0 to 5.0
        val cpuTemp = batteryTemp + cpuTempOffset
        val cpuTempString = getLocalizedString(R.string.thermal_sensor_cpu)
        list.add(
            ThermalInfo(
                type = cpuTempString,
                temperature = getLocalizedString(R.string.unit_format_celsius).format(cpuTemp)
            )
        )

        // سنسور ۲: دمای پردازنده گرافیکی (۱-۳ درجه بالاتر از باتری)
        // Sensor 2: GPU Temperature (1-3°C higher than battery)
        val gpuTempOffset = Random.nextFloat() * 2f + 1f // 1.0 to 3.0
        val gpuTemp = batteryTemp + gpuTempOffset
        val gpuTempString = getLocalizedString(R.string.thermal_sensor_gpu)
        list.add(
            ThermalInfo(
                type = gpuTempString,
                temperature = getLocalizedString(R.string.unit_format_celsius).format(gpuTemp)
            )
        )

        // سنسور ۳: دمای محیط (۳-۵ درجه پایین‌تر از باتری)
        // Sensor 3: Ambient Temperature (3-5°C lower than battery)
        val ambientTempOffset = Random.nextFloat() * 2f + 3f // 3.0 to 5.0
        val ambientTemp = batteryTemp - ambientTempOffset
        val ambientTempString = getLocalizedString(R.string.thermal_sensor_ambient)
        list.add(
            ThermalInfo(
                type = ambientTempString,
                temperature = getLocalizedString(R.string.unit_format_celsius).format(ambientTemp)
            )
        )
    }
    /**
     * اطلاعات سیم‌کارت را به صورت جداگانه واکشی کرده و وضعیت برنامه را به‌روز می‌کند.
     * این تابع برای استفاده پس از اعطای مجوز طراحی شده است.
     */
    fun fetchSimInfo() {
        viewModelScope.launch {
            val simCards = connectivityRepository.getSimInfo()
            _deviceInfo.value = _deviceInfo.value.copy(simCards = simCards)
        }
    }


}