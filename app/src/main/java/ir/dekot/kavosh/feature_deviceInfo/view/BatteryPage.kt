package ir.dekot.kavosh.feature_deviceInfo.view

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import ir.dekot.kavosh.R
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel

@RequiresApi(Build.VERSION_CODES.R)
@Composable
fun BatteryPage(viewModel: DeviceInfoViewModel) {
    val batteryInfo by viewModel.batteryInfo.collectAsState()

    // کارت واحد شامل تمام اطلاعات باتری
    InfoCard(stringResource(R.string.category_battery)) {
        // بخش اطلاعات عمومی
        InfoSection("اطلاعات عمومی") {
            InfoRow(stringResource(R.string.battery_level), stringResource(R.string.unit_format_percent, batteryInfo.level))
            InfoRow(stringResource(R.string.battery_status), batteryInfo.status)
            InfoRow(stringResource(R.string.battery_technology), batteryInfo.technology)
            InfoRow(stringResource(R.string.battery_temperature), batteryInfo.temperature)
        }

        // بخش سلامت و ظرفیت
        InfoSection(stringResource(R.string.battery_health_details)) {
            InfoRow(stringResource(R.string.battery_health), batteryInfo.health)
            if (batteryInfo.designCapacity > 0) {
                InfoRow(stringResource(R.string.battery_capacity_design), stringResource(R.string.battery_unit_mah, batteryInfo.designCapacity))
            }

        }

        // بخش شارژ و دشارژ
        InfoSection(stringResource(R.string.battery_charge_stats)) {
            InfoRow(stringResource(R.string.battery_charge_current), stringResource(R.string.battery_unit_ma, batteryInfo.chargeCurrent))
            InfoRow(stringResource(R.string.battery_voltage), batteryInfo.voltage)
            InfoRow(stringResource(R.string.battery_charge_power), stringResource(R.string.battery_unit_watt, batteryInfo.chargePower))
        }
    }
}