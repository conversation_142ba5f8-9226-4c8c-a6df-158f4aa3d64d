package ir.dekot.kavosh.feature_deviceInfo.model

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.telephony.TelephonyManager
import android.util.Log
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import ir.dekot.kavosh.R
import java.net.Inet4Address
import java.net.Inet6Address
import java.net.NetworkInterface
import java.util.Collections
import javax.inject.Inject
import javax.inject.Singleton

@Suppress("DEPRECATION")
@Singleton
class NetworkDataSource @Inject constructor(@param:ApplicationContext private val context: Context) {

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    private val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

    private fun isTetheringActive(): Boolean {
        try {
            val method = connectivityManager.javaClass.getMethod("isTetheringOn")
            return method.invoke(connectivityManager) as? Boolean == true
        } catch (_: Exception) {
            try {
                val method = wifiManager.javaClass.getMethod("isWifiApEnabled")
                return method.invoke(wifiManager) as? Boolean == true
            } catch (_: Exception) {
                return false
            }
        }
    }

    @SuppressLint("MissingPermission")
    fun getNetworkInfo(): NetworkInfo {
        val isHotspotOn = isTetheringActive()
        val (ipv4, ipv6) = getIpAddresses()

        for (network in connectivityManager.allNetworks) {
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: continue

            if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                val wifiInfo = wifiManager.connectionInfo
                val dhcpInfo = wifiManager.dhcpInfo

                return NetworkInfo(
                    networkType = "Wi-Fi",
                    ipAddressV4 = ipv4,
                    ipAddressV6 = ipv6,
                    isHotspotEnabled = isHotspotOn,
                    ssid = wifiInfo.ssid.removeSurrounding("\""),
                    bssid = wifiInfo.bssid,
                    linkSpeed = "${wifiInfo.linkSpeed} Mbps",
                    wifiSignalStrength = "${wifiInfo.rssi} dBm",
                    dns1 = intToIp(dhcpInfo.dns1),
                    dns2 = intToIp(dhcpInfo.dns2)
                )
            }

            if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                return NetworkInfo(
                    networkType = getMobileNetworkType(),
                    ipAddressV4 = ipv4,
                    ipAddressV6 = ipv6,
                    isHotspotEnabled = isHotspotOn,
                    networkOperator = telephonyManager.networkOperatorName,
                    mobileSignalStrength = getMobileSignalStrength()
                )
            }
        }

        return NetworkInfo(
            networkType = context.getString(R.string.label_disconnected),
            isHotspotEnabled = isHotspotOn
        )
    }

    private fun getIpAddresses(): Pair<String, String> {
        try {
            val allAddresses = Collections.list(NetworkInterface.getNetworkInterfaces())
                .flatMap { networkInterface ->
                    Collections.list(networkInterface.inetAddresses)
                        .filter { !it.isLoopbackAddress && it.hostAddress != null }
                }

            val ipv4 = allAddresses.firstOrNull { it is Inet4Address }?.hostAddress ?: context.getString(R.string.label_undefined)

            val ipv6 = allAddresses.firstOrNull { it is Inet6Address }?.let {
                val rawAddress = it.hostAddress
                val scopeIndex = rawAddress?.indexOf('%')
                scopeIndex?.let { it1 -> if (it1 > 0) rawAddress.substring(0, scopeIndex) else rawAddress }
            }?.uppercase() ?: context.getString(R.string.label_undefined)

            return ipv4 to ipv6
        } catch (_: Exception) {
            return context.getString(R.string.label_undefined) to context.getString(R.string.label_undefined)
        }
    }

    private fun intToIp(i: Int): String {
        return (i and 0xFF).toString() + "." +
                (i shr 8 and 0xFF) + "." +
                (i shr 16 and 0xFF) + "." +
                (i shr 24 and 0xFF)
    }

    private fun getMobileSignalStrength(): String {
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
            return context.getString(R.string.label_permission_required)
        }
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val dbm = telephonyManager.signalStrength?.cellSignalStrengths?.firstOrNull()?.dbm
            dbm?.let { "$it dBm" } ?: context.getString(R.string.label_not_available)
        } else {
            try {
                val signalStrength = telephonyManager.signalStrength
                val dbm = signalStrength?.let {
                    if (it.isGsm) (it.gsmSignalStrength * 2) - 113 else it.cdmaDbm
                }
                dbm?.let { "$it dBm" } ?: context.getString(R.string.label_not_available)
            } catch (_: SecurityException) {
                context.getString(R.string.label_permission_required)
            }
        }
    }

    private fun getMobileNetworkType(): String {
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
            return context.getString(R.string.label_permission_required)
        }

        Log.d("NetworkDataSource", "Starting network type detection...")

        // ابتدا تمام روش‌های ممکن برای تشخیص 5G را امتحان می‌کنیم
        // First try all possible methods to detect 5G
        val fiveGResult = detect5GNetwork()
        if (fiveGResult != null) {
            Log.d("NetworkDataSource", "5G detected: $fiveGResult")
            return fiveGResult
        }

        // اگر 5G تشخیص داده نشد، از روش‌های معمولی استفاده می‌کنیم
        // If 5G not detected, use standard methods
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            getNetworkTypeModern()
        } else {
            getNetworkTypeLegacy()
        }
    }

    /**
     * تشخیص جامع 5G با استفاده از تمام روش‌های ممکن
     * Comprehensive 5G detection using all possible methods
     */
    @SuppressLint("MissingPermission")
    private fun detect5GNetwork(): String? {
        return try {
            Log.d("NetworkDataSource", "Attempting comprehensive 5G detection...")

            // روش 1: بررسی مستقیم dataNetworkType
            // Method 1: Direct dataNetworkType check
            val networkType = telephonyManager.dataNetworkType
            Log.d("NetworkDataSource", "Current network type: $networkType (NR=${TelephonyManager.NETWORK_TYPE_NR})")

            if (networkType == TelephonyManager.NETWORK_TYPE_NR) {
                return "5G"
            }

            // روش 2: بررسی از طریق getNetworkType (deprecated اما کارآمد)
            // Method 2: Check through getNetworkType (deprecated but effective)
            try {
                @Suppress("DEPRECATION")
                val legacyNetworkType = telephonyManager.networkType
                Log.d("NetworkDataSource", "Legacy network type: $legacyNetworkType")

                if (legacyNetworkType == TelephonyManager.NETWORK_TYPE_NR) {
                    return "5G"
                }
            } catch (e: Exception) {
                Log.d("NetworkDataSource", "Legacy network type check failed: ${e.message}")
            }

            // روش 3: بررسی از طریق ServiceState
            // Method 3: ServiceState check
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val serviceState = telephonyManager.serviceState
                serviceState?.let { state ->
                    try {
                        // بررسی NR State
                        val nrStateMethod = state.javaClass.getMethod("getNrState")
                        val nrState = nrStateMethod.invoke(state) as? Int
                        Log.d("NetworkDataSource", "NR State: $nrState")

                        // مقادیر NR State:
                        // 0 = NONE, 1 = RESTRICTED, 2 = NOT_RESTRICTED, 3 = CONNECTED
                        if (nrState != null && nrState == 3) {
                            return "5G"
                        }
                    } catch (e: Exception) {
                        Log.d("NetworkDataSource", "NR State check failed: ${e.message}")
                    }
                }
            }

            // روش 4: بررسی از طریق getAllCellInfo
            // Method 4: Check through getAllCellInfo
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                try {
                    val cellInfos = telephonyManager.allCellInfo
                    Log.d("NetworkDataSource", "Cell infos count: ${cellInfos?.size}")

                    cellInfos?.forEach { cellInfo ->
                        val cellInfoType = cellInfo.javaClass.simpleName
                        Log.d("NetworkDataSource", "Cell Info Type: $cellInfoType")

                        // بررسی CellInfoNr برای 5G
                        if (cellInfoType.contains("Nr", ignoreCase = true) ||
                            cellInfoType.contains("5G", ignoreCase = true)) {
                            Log.d("NetworkDataSource", "5G detected via CellInfo: $cellInfoType")
                            return "5G"
                        }
                    }
                } catch (e: Exception) {
                    Log.d("NetworkDataSource", "getAllCellInfo failed: ${e.message}")
                }
            }

            // روش 5: بررسی از طریق SubscriptionManager (برای دستگاه‌های دو سیم کارته)
            // Method 5: Check through SubscriptionManager (for dual SIM devices)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                try {
                    val subscriptionManager = context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as? android.telephony.SubscriptionManager
                    subscriptionManager?.activeSubscriptionInfoList?.forEach { subInfo ->
                        try {
                            val subTelephonyManager = telephonyManager.createForSubscriptionId(subInfo.subscriptionId)
                            val subNetworkType = subTelephonyManager.dataNetworkType
                            Log.d("NetworkDataSource", "Subscription ${subInfo.subscriptionId} network type: $subNetworkType")

                            if (subNetworkType == TelephonyManager.NETWORK_TYPE_NR) {
                                Log.d("NetworkDataSource", "5G detected via subscription ${subInfo.subscriptionId}")
                                return "5G"
                            }
                        } catch (e: Exception) {
                            Log.d("NetworkDataSource", "Subscription check failed for ${subInfo.subscriptionId}: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.d("NetworkDataSource", "SubscriptionManager check failed: ${e.message}")
                }
            }

            null
        } catch (e: Exception) {
            Log.e("NetworkDataSource", "Error in detect5GNetwork: ${e.message}")
            null
        }
    }

    /**
     * روش جدید برای تشخیص نوع شبکه (API 29+)
     * Modern method for network type detection (API 29+)
     */
    @SuppressLint("MissingPermission")
    private fun getNetworkTypeModern(): String {
        return try {
            // روش اول: بررسی مستقیم dataNetworkType
            // Method 1: Direct check of dataNetworkType
            val networkType = telephonyManager.dataNetworkType

            // لاگ برای دیباگ
            // Log for debugging
            android.util.Log.d("NetworkDataSource", "Network Type: $networkType")

            // اگر NR (5G) تشخیص داده شد
            // If NR (5G) is detected
            if (networkType == TelephonyManager.NETWORK_TYPE_NR) {
                android.util.Log.d("NetworkDataSource", "5G detected via NETWORK_TYPE_NR")
                return "5G"
            }

            // روش دوم: بررسی از طریق ServiceState
            // Method 2: Check through ServiceState
            val serviceState = telephonyManager.serviceState
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && serviceState != null) {
                try {
                    // بررسی NR State
                    val nrStateMethod = serviceState.javaClass.getMethod("getNrState")
                    val nrState = nrStateMethod.invoke(serviceState) as? Int
                    android.util.Log.d("NetworkDataSource", "NR State: $nrState")

                    // اگر NR فعال است
                    if (nrState != null && nrState >= 3) {
                        android.util.Log.d("NetworkDataSource", "5G detected via NR State")
                        return "5G"
                    }

                    // بررسی NR Frequency Range
                    try {
                        val nrFrequencyRangeMethod = serviceState.javaClass.getMethod("getNrFrequencyRange")
                        val frequencyRange = nrFrequencyRangeMethod.invoke(serviceState) as? Int
                        android.util.Log.d("NetworkDataSource", "NR Frequency Range: $frequencyRange")

                        if (frequencyRange != null && frequencyRange > 0) {
                            android.util.Log.d("NetworkDataSource", "5G detected via NR Frequency Range")
                            return "5G"
                        }
                    } catch (e: Exception) {
                        android.util.Log.d("NetworkDataSource", "NR Frequency Range not available: ${e.message}")
                    }
                } catch (e: Exception) {
                    android.util.Log.d("NetworkDataSource", "ServiceState methods not available: ${e.message}")
                }
            }

            // برای سایر موارد از روش معمولی استفاده می‌کنیم
            // For other cases, use the standard method
            when (networkType) {
                TelephonyManager.NETWORK_TYPE_GPRS,
                TelephonyManager.NETWORK_TYPE_EDGE,
                TelephonyManager.NETWORK_TYPE_CDMA,
                TelephonyManager.NETWORK_TYPE_1xRTT,
                TelephonyManager.NETWORK_TYPE_IDEN -> "2G"
                TelephonyManager.NETWORK_TYPE_UMTS,
                TelephonyManager.NETWORK_TYPE_EVDO_0,
                TelephonyManager.NETWORK_TYPE_EVDO_A,
                TelephonyManager.NETWORK_TYPE_HSDPA,
                TelephonyManager.NETWORK_TYPE_HSUPA,
                TelephonyManager.NETWORK_TYPE_HSPA,
                TelephonyManager.NETWORK_TYPE_EVDO_B,
                TelephonyManager.NETWORK_TYPE_EHRPD,
                TelephonyManager.NETWORK_TYPE_HSPAP -> "3G"
                TelephonyManager.NETWORK_TYPE_LTE -> {
                    // برای LTE، بررسی اضافی برای 5G NSA انجام می‌دهیم
                    // For LTE, perform additional check for 5G NSA
                    android.util.Log.d("NetworkDataSource", "LTE detected, checking for 5G NSA")

                    // روش سوم: بررسی از طریق getAllCellInfo
                    // Method 3: Check through getAllCellInfo
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        try {
                            val cellInfos = telephonyManager.allCellInfo
                            cellInfos?.forEach { cellInfo ->
                                val cellInfoType = cellInfo.javaClass.simpleName
                                android.util.Log.d("NetworkDataSource", "Cell Info Type: $cellInfoType")

                                // بررسی CellInfoNr برای 5G
                                if (cellInfoType.contains("Nr", ignoreCase = true)) {
                                    android.util.Log.d("NetworkDataSource", "5G detected via CellInfoNr")
                                    return "5G"
                                }
                            }
                        } catch (e: Exception) {
                            android.util.Log.d("NetworkDataSource", "getAllCellInfo failed: ${e.message}")
                        }
                    }

                    "4G"
                }
                else -> {
                    android.util.Log.d("NetworkDataSource", "Unknown network type: $networkType")
                    context.getString(R.string.category_network)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("NetworkDataSource", "Error in getNetworkTypeModern: ${e.message}")
            getNetworkTypeLegacy()
        }
    }

    /**
     * روش قدیمی برای تشخیص نوع شبکه (API < 29)
     * Legacy method for network type detection (API < 29)
     */
    @SuppressLint("MissingPermission")
    private fun getNetworkTypeLegacy(): String {
        return when (telephonyManager.dataNetworkType) {
            TelephonyManager.NETWORK_TYPE_GPRS,
            TelephonyManager.NETWORK_TYPE_EDGE,
            TelephonyManager.NETWORK_TYPE_CDMA,
            TelephonyManager.NETWORK_TYPE_1xRTT,
            TelephonyManager.NETWORK_TYPE_IDEN -> "2G"
            TelephonyManager.NETWORK_TYPE_UMTS,
            TelephonyManager.NETWORK_TYPE_EVDO_0,
            TelephonyManager.NETWORK_TYPE_EVDO_A,
            TelephonyManager.NETWORK_TYPE_HSDPA,
            TelephonyManager.NETWORK_TYPE_HSUPA,
            TelephonyManager.NETWORK_TYPE_HSPA,
            TelephonyManager.NETWORK_TYPE_EVDO_B,
            TelephonyManager.NETWORK_TYPE_EHRPD,
            TelephonyManager.NETWORK_TYPE_HSPAP -> "3G"
            TelephonyManager.NETWORK_TYPE_LTE -> "4G"
            TelephonyManager.NETWORK_TYPE_NR -> "5G"
            else -> context.getString(R.string.category_network) // "Mobile"
        }
    }

    /**
     * بررسی دسترسی 5G برای حالت NSA و SA
     * Check 5G availability for NSA and SA modes
     */
    @SuppressLint("MissingPermission")
    private fun check5GAvailability(): String? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // روش اول: بررسی از طریق ServiceState
                // Method 1: Check through ServiceState
                val serviceState = telephonyManager.serviceState
                serviceState?.let { state ->
                    // بررسی NR State
                    // Check NR State
                    val nrState = try {
                        val method = state.javaClass.getMethod("getNrState")
                        val result = method.invoke(state) as? Int
                        result
                    } catch (e: Exception) {
                        null
                    }

                    // اگر NR فعال است (مقدار 3 معمولاً نشان‌دهنده اتصال فعال است)
                    // If NR is active (value 3 usually indicates active connection)
                    if (nrState != null && nrState >= 3) {
                        return "5G"
                    }

                    // بررسی اضافی برای NR Frequency Range
                    // Additional check for NR Frequency Range
                    try {
                        val nrFrequencyRange = state.javaClass.getMethod("getNrFrequencyRange")
                        val frequencyRange = nrFrequencyRange.invoke(state) as? Int
                        if (frequencyRange != null && frequencyRange > 0) {
                            return "5G"
                        }
                    } catch (e: Exception) {
                        // Ignore
                    }
                }

                // روش دوم: بررسی مستقیم dataNetworkType
                // Method 2: Direct check of dataNetworkType
                if (telephonyManager.dataNetworkType == TelephonyManager.NETWORK_TYPE_NR) {
                    return "5G"
                }

                // روش سوم: بررسی از طریق NetworkCapabilities
                // Method 3: Check through NetworkCapabilities
                for (network in connectivityManager.allNetworks) {
                    val capabilities = connectivityManager.getNetworkCapabilities(network)
                    if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true) {
                        // بررسی اضافی برای 5G از طریق capabilities
                        // Additional 5G check through capabilities
                        try {
                            val method = capabilities.javaClass.getMethod("hasCapability", Int::class.java)
                            // NetworkCapabilities.NET_CAPABILITY_NOT_METERED معمولاً برای 5G استفاده می‌شود
                            // NetworkCapabilities.NET_CAPABILITY_NOT_METERED is usually used for 5G
                            val hasHighSpeed = method.invoke(capabilities, 11) as? Boolean
                            if (hasHighSpeed == true && telephonyManager.dataNetworkType >= TelephonyManager.NETWORK_TYPE_LTE) {
                                // احتمال بالای 5G
                                // High probability of 5G
                                return "5G"
                            }
                        } catch (e: Exception) {
                            // Ignore
                        }
                    }
                }
            }

            null
        } catch (e: Exception) {
            null
        }
    }
}