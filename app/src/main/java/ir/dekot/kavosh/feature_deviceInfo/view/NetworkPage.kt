package ir.dekot.kavosh.feature_deviceInfo.view

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import ir.dekot.kavosh.R
import ir.dekot.kavosh.core.ui.shared_components.SectionTitleInCard
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel

@RequiresApi(Build.VERSION_CODES.R)
@Composable
fun NetworkPage(viewModel: DeviceInfoViewModel) {
    val deviceInfo by viewModel.deviceInfo.collectAsState()
    val downloadSpeed by viewModel.downloadSpeed.collectAsState()
    val uploadSpeed by viewModel.uploadSpeed.collectAsState()
    val networkInfo = deviceInfo.network

    // کارت واحد شامل تمام اطلاعات شبکه
    InfoCard(stringResource(R.string.network_info_title)) {
        // بخش اطلاعات عمومی شبکه
        InfoSection("اطلاعات عمومی") {
            InfoRow(stringResource(R.string.network_download_speed), downloadSpeed)
            InfoRow(stringResource(R.string.network_upload_speed), uploadSpeed)
            InfoRow(
                stringResource(R.string.network_hotspot_status),
                stringResource(if (networkInfo.isHotspotEnabled) R.string.label_on else R.string.label_off)
            )
            InfoRow(stringResource(R.string.network_connection_type), networkInfo.networkType)
            InfoRow(stringResource(R.string.network_ipv4), networkInfo.ipAddressV4)
            InfoRow(stringResource(R.string.network_ipv6), networkInfo.ipAddressV6)
        }

        // بخش جزئیات Wi-Fi (در صورت اتصال)
        if (networkInfo.networkType == "Wi-Fi") {
            InfoSection(stringResource(R.string.network_wifi_details)) {
                InfoRow(stringResource(R.string.network_ssid), networkInfo.ssid)
                InfoRow(stringResource(R.string.network_bssid), networkInfo.bssid)
                InfoRow(stringResource(R.string.network_signal_strength), networkInfo.wifiSignalStrength)
                InfoRow(stringResource(R.string.network_link_speed), networkInfo.linkSpeed)
                InfoRow(stringResource(R.string.network_dns1), networkInfo.dns1)
                InfoRow(stringResource(R.string.network_dns2), networkInfo.dns2)
            }
        } else if (networkInfo.networkType != stringResource(R.string.label_disconnected)) {
            // بخش جزئیات شبکه موبایل (در صورت اتصال)
            InfoSection(stringResource(R.string.network_mobile_details)) {
                InfoRow(stringResource(R.string.network_operator), networkInfo.networkOperator)
                InfoRow(stringResource(R.string.network_signal_strength), networkInfo.mobileSignalStrength)
            }
        }
    }
}