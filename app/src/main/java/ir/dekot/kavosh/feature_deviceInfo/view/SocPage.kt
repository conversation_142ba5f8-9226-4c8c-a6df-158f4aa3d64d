package ir.dekot.kavosh.feature_deviceInfo.view

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import ir.dekot.kavosh.R
import ir.dekot.kavosh.core.ui.shared_components.SectionTitleInCard
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel

@RequiresApi(Build.VERSION_CODES.R)
@Composable
fun SocPage(viewModel: DeviceInfoViewModel) {

    // بهینه‌سازی ۵: استفاده از derivedStateOf برای بهبود recomposition
    // Optimization 5: Using derivedStateOf for improved recomposition
    val deviceInfo by viewModel.deviceInfo.collectAsState()
    val liveCpuFrequencies by viewModel.liveCpuFrequencies.collectAsState()

    // بهینه‌سازی ۵: محاسبه مشتق شده برای جلوگیری از recomposition غیرضروری
    // Optimization 5: Derived computation to prevent unnecessary recomposition
    val cpuInfo by remember { derivedStateOf { deviceInfo.cpu } }
    val gpuInfo by remember { derivedStateOf { deviceInfo.gpu } }
    val ramInfo by remember { derivedStateOf { deviceInfo.ram } }
    val displayInfo by remember { derivedStateOf { deviceInfo.display } }
    val storageInfo by remember { derivedStateOf { deviceInfo.storage } }

    // کارت واحد شامل تمام اطلاعات SOC
    InfoCard("اطلاعات SOC") {
        // بخش CPU
        InfoSection(stringResource(R.string.cpu_title)) {
            InfoRow(stringResource(R.string.cpu_model), cpuInfo.model)
            InfoRow(stringResource(R.string.cpu_topology), cpuInfo.topology)
            InfoRow(stringResource(R.string.cpu_process), cpuInfo.process)
            InfoRow(stringResource(R.string.cpu_architecture), cpuInfo.architecture)
            InfoRow(stringResource(R.string.cpu_core_count), cpuInfo.coreCount.toString())

            SectionTitleInCard(title = stringResource(R.string.cpu_live_speed))

            val freqsToShow = (liveCpuFrequencies.ifEmpty { cpuInfo.liveFrequencies })
                .mapIndexed { index, freq -> Pair(index, freq) }
                .chunked(2)

            freqsToShow.forEach { rowItems ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    rowItems.forEach { (index, freq) ->
                        Column(modifier = Modifier.weight(1f)) {
                            InfoRow(stringResource(R.string.cpu_core_prefix, index), freq)

                            val maxFreq = cpuInfo.maxFrequenciesKhz.getOrElse(index) { 0L }
                            val currentFreq = freq.split(" ")[0].toLongOrNull() ?: 0L
                            val progress = if (maxFreq > 0) (currentFreq * 1000) / maxFreq.toFloat() else 0f
                            val animatedProgress by animateFloatAsState(targetValue = progress, label = "CpuProgress$index")

                            LinearProgressIndicator(
                                progress = { animatedProgress },
                                modifier = Modifier.fillMaxWidth().height(5.dp)
                            )
                        }
                    }
                    if (rowItems.size == 1) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
            }

            Spacer(modifier = Modifier.height(8.dp))
            SectionTitleInCard(title = stringResource(R.string.cpu_clock_range))
            cpuInfo.clockSpeedRanges.forEach { range ->
                val parts = range.split(":")
                InfoRow(parts.getOrElse(0) { "" }, parts.getOrElse(1) { "" }.trim())
            }
        }

        // بخش GPU
        InfoSection(stringResource(R.string.gpu_title)) {
            InfoRow(stringResource(R.string.gpu_model), gpuInfo.model)
            InfoRow(stringResource(R.string.gpu_vendor), gpuInfo.vendor)
        }

        // بخش RAM
        InfoSection(stringResource(R.string.ram_title)) {
            InfoRow(stringResource(R.string.ram_total), ramInfo.total)
            InfoRow(stringResource(R.string.ram_available), ramInfo.available)
        }

        // بخش Display
        InfoSection(stringResource(R.string.display_title)) {
            InfoRow(stringResource(R.string.display_resolution), displayInfo.resolution)
            InfoRow(stringResource(R.string.display_density), displayInfo.density)
            InfoRow(stringResource(R.string.display_refresh_rate), displayInfo.refreshRate)
        }

        // بخش Storage
        InfoSection(stringResource(R.string.storage_title)) {
            InfoRow(stringResource(R.string.storage_total), storageInfo.total)
            InfoRow(stringResource(R.string.storage_available), storageInfo.available)
        }
    }
}