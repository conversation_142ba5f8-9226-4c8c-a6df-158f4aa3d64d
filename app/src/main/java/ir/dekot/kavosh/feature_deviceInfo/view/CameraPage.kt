package ir.dekot.kavosh.feature_deviceInfo.view

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import ir.dekot.kavosh.R
import ir.dekot.kavosh.core.ui.shared_components.EmptyStateMessage
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel

@RequiresApi(Build.VERSION_CODES.R)
@Composable
fun CameraPage(viewModel: DeviceInfoViewModel) {
    val deviceInfo by viewModel.deviceInfo.collectAsState()

    if (deviceInfo.cameras.isEmpty()) {
        EmptyStateMessage("No cameras found or access is not possible.")
    } else {
        // کارت واحد شامل تمام اطلاعات دوربین‌ها
        InfoCard("اطلاعات دوربین") {
            deviceInfo.cameras.forEachIndexed { index, camera ->
                // بخش هر دوربین
                InfoSection(camera.name) {
                    InfoRow(stringResource(R.string.camera_megapixels), camera.megapixels)
                    InfoRow(stringResource(R.string.camera_max_resolution), camera.maxResolution)
                    InfoRow(
                        stringResource(R.string.camera_flash_support),
                        stringResource(if (camera.hasFlash) R.string.label_yes else R.string.label_no)
                    )
                    InfoRow(stringResource(R.string.camera_apertures), camera.apertures)
                    InfoRow(stringResource(R.string.camera_focal_lengths), camera.focalLengths)
                    InfoRow(stringResource(R.string.camera_sensor_size), camera.sensorSize)
                }
            }
        }
    }
}