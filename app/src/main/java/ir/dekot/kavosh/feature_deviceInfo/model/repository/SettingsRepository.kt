package ir.dekot.kavosh.feature_deviceInfo.model.repository

import ir.dekot.kavosh.feature_customeTheme.ColorTheme
import ir.dekot.kavosh.feature_customeTheme.CustomColorTheme
import ir.dekot.kavosh.feature_customeTheme.PredefinedColorTheme
import ir.dekot.kavosh.feature_customeTheme.Theme
import ir.dekot.kavosh.feature_deviceInfo.model.DeviceInfo
import ir.dekot.kavosh.feature_deviceInfo.model.InfoCategory
import ir.dekot.kavosh.feature_settings.model.SettingsDataSource
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مخزن تنظیمات - مسئول مدیریت تمام تنظیمات برنامه
 * شامل زبان، تم، داشبورد، کش و تم‌های رنگی
 */
@Singleton
class SettingsRepository @Inject constructor(
    private val settingsDataSource: SettingsDataSource
) {

    // --- مدیریت زبان ---

    /**
     * ذخیره زبان انتخاب شده
     * @param language کد زبان (مثل "fa" یا "en")
     */
    fun saveLanguage(language: String) = settingsDataSource.saveLanguage(language)

    /**
     * دریافت زبان فعلی
     * @return کد زبان فعلی
     */
    fun getLanguage(): String = settingsDataSource.getLanguage()

    // --- مدیریت اولین اجرا ---

    /**
     * بررسی اولین اجرای برنامه
     * @return true اگر اولین اجرا باشد
     */
    fun isFirstLaunch(): Boolean = settingsDataSource.isFirstLaunch()

    /**
     * تنظیم تکمیل اولین اجرا
     */
    fun setFirstLaunchCompleted() = settingsDataSource.setFirstLaunchCompleted()

    // --- مدیریت تم ---

    /**
     * ذخیره تم انتخاب شده
     * @param theme تم مورد نظر
     */
    fun saveTheme(theme: Theme) = settingsDataSource.saveTheme(theme)

    /**
     * دریافت تم فعلی
     * @return تم فعلی برنامه
     */
    fun getTheme(): Theme = settingsDataSource.getTheme()



    // --- مدیریت داشبورد ---

    /**
     * ذخیره ترتیب دسته‌بندی‌های داشبورد
     * @param categories لیست دسته‌بندی‌ها به ترتیب مطلوب
     */
    fun saveDashboardOrder(categories: List<InfoCategory>) = settingsDataSource.saveDashboardOrder(categories)

    /**
     * دریافت ترتیب دسته‌بندی‌های داشبورد
     * @return لیست دسته‌بندی‌ها به ترتیب ذخیره شده
     */
    fun getDashboardOrder(): List<InfoCategory> = settingsDataSource.getDashboardOrder()

    /**
     * ذخیره دسته‌بندی‌های مخفی
     * @param hidden مجموعه دسته‌بندی‌های مخفی
     */
    fun saveHiddenCategories(hidden: Set<InfoCategory>) = settingsDataSource.saveHiddenCategories(hidden)

    /**
     * دریافت دسته‌بندی‌های مخفی
     * @return مجموعه دسته‌بندی‌های مخفی
     */
    fun getHiddenCategories(): Set<InfoCategory> = settingsDataSource.getHiddenCategories()

    /**
     * فعال/غیرفعال کردن قابلیت جابجایی
     * @param enabled وضعیت قابلیت جابجایی
     */
    fun setReorderingEnabled(enabled: Boolean) = settingsDataSource.setReorderingEnabled(enabled)

    /**
     * بررسی فعال بودن قابلیت جابجایی
     * @return true اگر جابجایی فعال باشد
     */
    fun isReorderingEnabled(): Boolean = settingsDataSource.isReorderingEnabled()

    // بهینه‌سازی ۴: مدیریت کش اطلاعات دستگاه با TTL
    // Optimization 4: Device info cache management with TTL

    /**
     * بهینه‌سازی ۴: ذخیره کش اطلاعات دستگاه با TTL
     * Optimization 4: Save device info cache with TTL
     */
    fun saveDeviceInfoCache(deviceInfo: DeviceInfo) = settingsDataSource.saveDeviceInfoCache(deviceInfo)

    /**
     * بهینه‌سازی ۴: دریافت کش اطلاعات دستگاه با بررسی TTL
     * Optimization 4: Get device info cache with TTL validation
     */
    fun getDeviceInfoCache(): DeviceInfo? = settingsDataSource.getDeviceInfoCache()

    /**
     * بهینه‌سازی ۴: پاک کردن کش اطلاعات دستگاه
     * Optimization 4: Clear device info cache
     */
    fun clearDeviceInfoCache() = settingsDataSource.clearDeviceInfoCache()

    // --- مدیریت تم‌های رنگی ---

    /**
     * ذخیره تم رنگی از پیش تعریف شده
     * @param colorTheme تم رنگی انتخاب شده
     */
    fun savePredefinedColorTheme(colorTheme: PredefinedColorTheme) =
        settingsDataSource.savePredefinedColorTheme(colorTheme)

    /**
     * ذخیره تم رنگی سفارشی
     * @param customTheme تم رنگی سفارشی
     */
    fun saveCustomColorTheme(customTheme: CustomColorTheme) =
        settingsDataSource.saveCustomColorTheme(customTheme)

    /**
     * دریافت تم رنگی فعلی
     * @return تم رنگی فعلی یا null
     */
    fun getCurrentColorTheme(): ColorTheme? = settingsDataSource.getCurrentColorTheme()

    /**
     * بازنشانی تم رنگی به حالت پیش‌فرض
     */
    fun resetColorTheme() = settingsDataSource.resetColorTheme()

    /**
     * بررسی وجود تم رنگی سفارشی
     * @return true اگر تم سفارشی موجود باشد
     */
    fun hasCustomColorTheme(): Boolean = settingsDataSource.hasCustomColorTheme()

    // بهینه‌سازی ۴: متدهای مدیریت کش پیشرفته
    // Optimization 4: Advanced cache management methods

    /**
     * بهینه‌سازی ۴: محاسبه اندازه کل کش
     * Optimization 4: Calculate total cache size
     */
    fun getCacheSizeBytes(): Long = settingsDataSource.getCacheSizeBytes()

    /**
     * بهینه‌سازی ۴: پاکسازی کش‌های منقضی شده
     * Optimization 4: Clean up expired caches
     */
    fun cleanupExpiredCaches() = settingsDataSource.cleanupExpiredCaches()

    /**
     * بهینه‌سازی ۴: پاکسازی تمام کش‌ها
     * Optimization 4: Clear all caches
     */
    fun clearAllCaches() = settingsDataSource.clearAllCaches()

    /**
     * بهینه‌سازی ۴: بررسی نیاز به پاکسازی کش
     * Optimization 4: Check if cache cleanup is needed
     */
    fun isCacheCleanupNeeded(): Boolean = settingsDataSource.isCacheCleanupNeeded()

    /**
     * بهینه‌سازی ۴: پاکسازی هوشمند کش
     * Optimization 4: Smart cache cleanup
     */
    fun performSmartCacheCleanup() = settingsDataSource.performSmartCacheCleanup()
}
