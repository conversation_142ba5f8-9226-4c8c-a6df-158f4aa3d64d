package ir.dekot.kavosh.feature_deviceInfo.model.repository

import android.app.Activity
import android.os.Build
import androidx.annotation.RequiresApi
import ir.dekot.kavosh.feature_deviceInfo.model.DeviceInfo
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مخزن اصلی اطلاعات دستگاه - مسئول تجمیع اطلاعات از مخازن تخصصی
 * Repository اصلی که اطلاعات کامل دستگاه را از مخازن مختلف جمع‌آوری می‌کند
 */
@Singleton
class DeviceInfoRepository @Inject constructor(
    private val hardwareRepository: HardwareRepository,
    private val systemRepository: SystemRepository,
    private val powerRepository: PowerRepository,
    private val connectivityRepository: ConnectivityRepository,
    private val applicationRepository: ApplicationRepository,
    private val cameraRepository: CameraRepository
) {

    /**
     * بهینه‌سازی ۳: دریافت اطلاعات کامل دستگاه با threading بهینه
     * Optimization 3: Get complete device info with optimized threading
     */
    @RequiresApi(Build.VERSION_CODES.R)
    suspend fun getDeviceInfo(activity: Activity): DeviceInfo {
        return DeviceInfo(
            cpu = hardwareRepository.getCpuInfo(),
            gpu = hardwareRepository.getGpuInfo(activity),
            ram = hardwareRepository.getRamInfo(),
            display = hardwareRepository.getDisplayInfo(activity),
            storage = hardwareRepository.getStorageInfo(),
            system = systemRepository.getSystemInfo(),
            network = connectivityRepository.getNetworkInfo(),
            sensors = hardwareRepository.getSensorInfo(activity),
            thermal = hardwareRepository.getThermalInfo(),
            cameras = cameraRepository.getCameraInfoList(),
            simCards = connectivityRepository.getSimInfo(),
            apps = applicationRepository.getInstalledApps()
        )
    }

    /**
     * بهینه‌سازی ۳: دریافت اطلاعات کامل دستگاه بدون Activity با threading بهینه
     * Optimization 3: Get basic device info without Activity with optimized threading
     */
    suspend fun getBasicDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            cpu = hardwareRepository.getCpuInfo(),
            ram = hardwareRepository.getRamInfo(),
            storage = hardwareRepository.getStorageInfo(),
            system = systemRepository.getSystemInfo(),
            network = connectivityRepository.getNetworkInfo(),
            thermal = hardwareRepository.getThermalInfo(),
            cameras = cameraRepository.getCameraInfoList(),
            apps = applicationRepository.getInstalledApps()
        )
    }
}