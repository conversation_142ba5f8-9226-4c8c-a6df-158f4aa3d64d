package ir.dekot.kavosh.feature_deviceInfo.view

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember

import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import ir.dekot.kavosh.R
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel

@RequiresApi(Build.VERSION_CODES.R)
@Composable
fun SystemPage(viewModel: DeviceInfoViewModel) {
    val deviceInfo by viewModel.deviceInfo.collectAsState()
    val systemInfo by remember { derivedStateOf { deviceInfo.system } }



    // کارت واحد شامل تمام اطلاعات سیستم
    InfoCard("اطلاعات سیستم") {
        // بخش اطلاعات اصلی سیستم
        InfoSection("اطلاعات اصلی") {
            InfoRow("نسخه اندروید", systemInfo.androidVersion)
            InfoRow("سطح SDK", systemInfo.sdkLevel)
            InfoRow("شماره بیلد", systemInfo.buildNumber)
            InfoRow("Security Patch Level", systemInfo.securityPatchLevel)
            InfoRow(
                "وضعیت Root",
                stringResource(if (systemInfo.isRooted) R.string.label_rooted else R.string.label_not_rooted)
            )
        }

        // بخش اطلاعات دستگاه
        InfoSection("اطلاعات دستگاه") {
            InfoRow("سازنده", systemInfo.deviceManufacturer)
            InfoRow("برند", systemInfo.deviceBrand)
            InfoRow("مدل", systemInfo.deviceModel)
            InfoRow("محصول", systemInfo.deviceProduct)
            InfoRow("برد", systemInfo.deviceBoard)
            InfoRow("سخت‌افزار", systemInfo.deviceHardware)
        }

        // بخش اطلاعات بیلد
        InfoSection("اطلاعات بیلد") {
            InfoRow("Build ID", systemInfo.buildId)
            InfoRow("Build Display", systemInfo.buildDisplay)
            InfoRow("Build Type", systemInfo.buildType)
            InfoRow("Build Tags", systemInfo.buildTags)
            InfoRow("Build User", systemInfo.buildUser)
            InfoRow("Build Host", systemInfo.buildHost)
            InfoRow("Build Time", systemInfo.buildTime)
            InfoRow("Bootloader", systemInfo.bootloader)
        }

        // بخش زبان و منطقه
        InfoSection("زبان و منطقه") {
            InfoRow("زبان سیستم", systemInfo.systemLanguage)
            InfoRow("کشور سیستم", systemInfo.systemCountry)
            InfoRow("منطقه زمانی", systemInfo.timeZone)
        }

        // بخش کرنل و Java
        InfoSection("کرنل و Java VM") {
            InfoRow("نسخه کرنل", systemInfo.kernelVersion)
            InfoRow("نسخه Java VM", systemInfo.javaVmVersion)
            InfoRow("سازنده Java VM", systemInfo.javaVmVendor)
        }

        // بخش اطلاعات اضافی
        InfoSection("اطلاعات اضافی") {
            InfoRow("نسخه رادیو", systemInfo.radioVersion)
            InfoRow("Build Fingerprint", systemInfo.buildFingerprint)
        }
    }
}