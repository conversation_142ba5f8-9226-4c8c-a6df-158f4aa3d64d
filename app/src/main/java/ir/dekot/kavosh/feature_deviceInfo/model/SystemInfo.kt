package ir.dekot.kavosh.feature_deviceInfo.model

import androidx.compose.runtime.Immutable
import kotlinx.serialization.Serializable

@Immutable
@Serializable
data class SystemInfo(
    // اطلاعات اصلی سیستم
    val androidVersion: String = "نامشخص",
    val sdkLevel: String = "نامشخص",
    val buildNumber: String = "نامشخص",
    val isRooted: Boolean = false,

    // اطلاعات دستگاه
    val deviceManufacturer: String = "نامشخص", // سازنده
    val deviceBrand: String = "نامشخص", // برند
    val deviceModel: String = "نامشخص", // مدل
    val deviceProduct: String = "نامشخص", // محصول
    val deviceBoard: String = "نامشخص", // برد
    val deviceHardware: String = "نامشخص", // سخت‌افزار

    // اطلاعات بیلد
    val buildId: String = "نامشخص", // Build ID
    val buildDisplay: String = "نامشخص", // Build Display
    val buildFingerprint: String = "نامشخص", // Build Fingerprint
    val buildTags: String = "نامشخص", // Build Tags
    val buildType: String = "نامشخص", // Build Type
    val buildUser: String = "نامشخص", // Build User
    val buildHost: String = "نامشخص", // Build Host
    val buildTime: String = "نامشخص", // Build Time

    // اطلاعات امنیتی
    val securityPatchLevel: String = "نامشخص", // Security Patch Level

    // اطلاعات زبان و منطقه
    val systemLanguage: String = "نامشخص", // زبان سیستم
    val systemCountry: String = "نامشخص", // کشور سیستم
    val timeZone: String = "نامشخص", // منطقه زمانی

    // اطلاعات کرنل و Java
    val kernelVersion: String = "نامشخص", // نسخه کرنل
    val javaVmVersion: String = "نامشخص", // نسخه Java VM
    val javaVmVendor: String = "نامشخص", // سازنده Java VM

    // اطلاعات اضافی
    val bootloader: String = "نامشخص", // Bootloader
    val radioVersion: String = "نامشخص" // Radio Version
)