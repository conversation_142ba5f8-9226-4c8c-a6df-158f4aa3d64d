package ir.dekot.kavosh.feature_deviceInfo.view.infoCards

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import ir.dekot.kavosh.R
import ir.dekot.kavosh.feature_deviceInfo.view.InfoCard
import ir.dekot.kavosh.feature_deviceInfo.view.InfoRow
import ir.dekot.kavosh.feature_deviceInfo.model.GpuInfo

@Composable
fun GpuInfoCard(info: GpuInfo) {
    InfoCard(stringResource(R.string.gpu_title)) {
        InfoRow(stringResource(R.string.gpu_model), info.model)
        InfoRow(stringResource(R.string.gpu_vendor), info.vendor)
    }
}