package ir.dekot.kavosh.feature_deviceInfo.view.infoCards

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import ir.dekot.kavosh.R
import ir.dekot.kavosh.feature_deviceInfo.model.RamInfo
import ir.dekot.kavosh.feature_deviceInfo.view.InfoCard
import ir.dekot.kavosh.feature_deviceInfo.view.InfoRow

@Composable
fun RamInfoCard(info: RamInfo) {
    InfoCard(stringResource(R.string.ram_title)) {
        InfoRow(stringResource(R.string.ram_total), info.total)
        InfoRow(stringResource(R.string.ram_available), info.available)
    }
}