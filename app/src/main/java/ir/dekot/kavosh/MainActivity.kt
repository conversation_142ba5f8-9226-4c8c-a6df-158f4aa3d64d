package ir.dekot.kavosh

import android.content.Context
import android.os.Build
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.LayoutDirection
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import dagger.hilt.android.AndroidEntryPoint
import ir.dekot.kavosh.core.navigation.DeviceInspectorApp
import ir.dekot.kavosh.core.navigation.NavigationViewModel
import ir.dekot.kavosh.core.permission.ui.AppPermissionsDialog
import ir.dekot.kavosh.core.permission.viewmodel.PermissionViewModel
import ir.dekot.kavosh.core.splash.SplashScreenManager
import ir.dekot.kavosh.core.ui.shared_components.AnimationOptimizationManager
import ir.dekot.kavosh.core.ui.theme.AnimationStateProvider
import ir.dekot.kavosh.core.ui.theme.ThemeTransitionOverlay
import ir.dekot.kavosh.core.util.LanguageManager
import ir.dekot.kavosh.core.util.LocaleProvider
import ir.dekot.kavosh.feature_customeTheme.Theme
import ir.dekot.kavosh.feature_customeTheme.theme.KavoshTheme
import ir.dekot.kavosh.feature_dashboard.viewModel.DashboardViewModel
import ir.dekot.kavosh.feature_deviceInfo.model.repository.SettingsRepository
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel
import ir.dekot.kavosh.feature_export_and_sharing.viewModel.DiagnosticExportViewModel
import ir.dekot.kavosh.feature_export_and_sharing.viewModel.ExportViewModel
import ir.dekot.kavosh.feature_settings.viewModel.SettingsViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var settingsRepository: SettingsRepository

    // بهینه‌سازی ۱: تنبل‌سازی ViewModelها برای بهبود زمان راه‌اندازی
    // Optimization 1: Lazy initialization of ViewModels for improved startup time
    private val deviceInfoViewModel: DeviceInfoViewModel by lazy { ViewModelProvider(this)[DeviceInfoViewModel::class.java] }
    private val settingsViewModel: SettingsViewModel by lazy { ViewModelProvider(this)[SettingsViewModel::class.java] }
    private val dashboardViewModel: DashboardViewModel by lazy { ViewModelProvider(this)[DashboardViewModel::class.java] }
    private val exportViewModel: ExportViewModel by lazy { ViewModelProvider(this)[ExportViewModel::class.java] }
    private val diagnosticExportViewModel: DiagnosticExportViewModel by lazy { ViewModelProvider(this)[DiagnosticExportViewModel::class.java] }
    private val navigationViewModel: NavigationViewModel by lazy { ViewModelProvider(this)[NavigationViewModel::class.java] }
    private val permissionViewModel: PermissionViewModel by lazy { ViewModelProvider(this)[PermissionViewModel::class.java] }

    // SplashScreenManager as regular class instance
    private lateinit var splashScreenManager: SplashScreenManager


    @RequiresApi(Build.VERSION_CODES.R)
    private val createFileLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument("*/*")
    ) { uri ->
        uri?.let {
            val format = exportViewModel.pendingExportFormat
            if (format != null) {
                // اطلاعات دستگاه را از ViewModel اصلی می‌خوانیم و به ViewModel خروجی پاس می‌دهیم
                val currentDeviceInfo = deviceInfoViewModel.deviceInfo.value
                exportViewModel.performExport(it, format, currentDeviceInfo)
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private val createDiagnosticFileLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument("*/*")
    ) { uri ->
        uri?.let {
            diagnosticExportViewModel.performExport(it)
        }
    }

    // Permission launcher برای درخواست پرمیشن‌ها
    // Permission launcher for requesting permissions
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        val currentPermission = currentRequestedPermission
        if (currentPermission != null) {
            permissionViewModel.onPermissionResult(currentPermission, isGranted)
            currentRequestedPermission = null
        }
    }

    // نگهداری پرمیشن فعلی در حال درخواست
    // Keep track of current permission being requested
    private var currentRequestedPermission: String? = null

    @RequiresApi(Build.VERSION_CODES.R)
    override fun attachBaseContext(newBase: Context) {
        // از متد استاتیک ViewModel استفاده می‌کنیم
        val lang = SettingsViewModel.getSavedLanguage(newBase)
        val localizedContext = LanguageManager.createLocalizedContext(newBase, lang)
        super.attachBaseContext(localizedContext)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // نصب صفحه اسپلش بر اساس نسخه اندروید
        // Install splash screen based on Android version
        val splashScreen = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // اندروید 12 و بالاتر - استفاده از API جدید
            // Android 12+ - Use new API
            installSplashScreen()
        } else {
            // اندروید 11 و پایین‌تر - splash screen سنتی از طریق theme مدیریت می‌شود
            // Android 11 and below - traditional splash screen managed through theme
            null
        }

        super.onCreate(savedInstanceState)

        // بهینه‌سازی ۷: راه‌اندازی مدیر بهینه‌سازی انیمیشن
        // Optimization 7: Initialize animation optimization manager
        AnimationOptimizationManager.initialize()

        // ایجاد SplashScreenManager پس از super.onCreate()
        // Create SplashScreenManager after super.onCreate()
        splashScreenManager = SplashScreenManager.create(this, settingsRepository)

        // پیکربندی صفحه اسپلش با بارگذاری داده‌ها
        // Configure splash screen with data loading
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // اندروید 12 و بالاتر - استفاده از SplashScreenManager
            // Android 12+ - Use SplashScreenManager
            splashScreenManager.configureSplashScreen(
                splashScreen = splashScreen!!,
                activity = this,
                deviceInfoViewModel = deviceInfoViewModel,
                dashboardViewModel = dashboardViewModel,
                onDataLoadingComplete = {
                    // داده‌ها آماده است، صفحه اسپلش می‌تواند بسته شود
                    // Data is ready, splash screen can be dismissed
                }
            )
        } else {
            // اندروید 11 و پایین‌تر - بارگذاری داده‌ها و تغییر theme
            // Android 11 and below - Load data and change theme
            splashScreenManager.startDataLoadingForTraditionalSplash(
                activity = this,
                deviceInfoViewModel = deviceInfoViewModel,
                dashboardViewModel = dashboardViewModel,
                onDataLoadingComplete = {
                    // تغییر theme به theme اصلی برنامه
                    // Change theme to main app theme
                    setTheme(R.style.Theme_Kavosh)
                }
            )
        }

        // به رویدادهای ViewModel جدید گوش می‌دهیم
        lifecycleScope.launch {
            exportViewModel.exportRequest.collectLatest { format ->
                val fileName = "Kavosh_Report_${System.currentTimeMillis()}.${format.extension}"
                createFileLauncher.launch(fileName)
            }
        }

        // مدیریت درخواست پرمیشن‌ها
        // Handle permission requests
        // بررسی پرمیشن‌ها پس از بارگذاری کامل داده‌ها
        // Check permissions after data loading is complete
        lifecycleScope.launch {
            splashScreenManager.isDataLoadingComplete.collect { isComplete ->
                if (isComplete) {
                    // تاخیر کوتاه برای اطمینان از نمایش کامل UI
                    // Short delay to ensure UI is fully displayed
                    kotlinx.coroutines.delay(500)
                    permissionViewModel.checkAndShowPermissionDialog()
                }
            }
        }

        // به رویدادهای DiagnosticExportViewModel گوش می‌دهیم
        lifecycleScope.launch {
            diagnosticExportViewModel.filePickerRequest.collectLatest { format ->
                val fileName = "Kavosh_Diagnostic_${System.currentTimeMillis()}.${format.extension}"
                createDiagnosticFileLauncher.launch(fileName)
            }
        }

        lifecycleScope.launch {
            // به رویداد تغییر سریع زبان گوش می‌دهیم
            settingsViewModel.languageChangeRequest.collectLatest { newLanguage ->
                // تغییر سریع زبان بدون recreate کامل
                LanguageManager.changeLanguageQuick(this@MainActivity, newLanguage)
            }
        }

        // حذف شده: بارگذاری داده‌ها حالا در صفحه Loading انجام می‌شود
        // lifecycleScope.launch {
        //     lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
        //         deviceInfoViewModel.loadDataForNonFirstLaunch(this@MainActivity)
        //     }
        // }

        enableEdgeToEdge()
        setContent {
            // زبان و تم را از ViewModel جدید می‌خوانیم
            val language by settingsViewModel.language.collectAsState()
            val currentTheme by settingsViewModel.themeState.collectAsState() // <-- خواندن تم
            val currentColorTheme by settingsViewModel.currentColorTheme.collectAsState() // <-- خواندن تم رنگی

            val useDarkTheme = when (currentTheme) {
                Theme.SYSTEM -> isSystemInDarkTheme()
                Theme.LIGHT -> false
                Theme.DARK -> true
            }

            val layoutDirection = if (language == "fa") LayoutDirection.Rtl else LayoutDirection.Ltr

            // وضعیت پرمیشن‌ها
            // Permission states
            val showPermissionDialog by permissionViewModel.showPermissionDialog.collectAsState()
            val permissions by permissionViewModel.permissions.collectAsState()

            // استفاده از LocaleProvider برای تغییر فوری زبان
            LocaleProvider(language = language) {
                CompositionLocalProvider(LocalLayoutDirection provides layoutDirection) {
                    // ارائه‌دهنده حالت انیمیشن برای مدیریت ریکامپوز
                    AnimationStateProvider {
                    // تم را اینجا بر اساس داده‌های SettingsViewModel تنظیم می‌کنیم
                    KavoshTheme(
                        darkTheme = useDarkTheme,
                        theme = currentTheme,
                        colorTheme = currentColorTheme // <-- پاس دادن تم رنگی
                    ) {
                        // استفاده از ThemeTransitionOverlay برای مدیریت انیمیشن تغییر تم
                        ThemeTransitionOverlay(
                            settingsViewModel = settingsViewModel,
                            currentColorTheme = currentColorTheme
                        ) {
                        Surface(
                            modifier = Modifier.fillMaxSize(),
                            color = MaterialTheme.colorScheme.background
                        ) {
                            DeviceInspectorApp(
                                // هر دو ViewModel را به تابع اصلی پاس می‌دهیم
                                deviceInfoViewModel = deviceInfoViewModel,
                                settingsViewModel = settingsViewModel,
                                dashboardViewModel = dashboardViewModel, // <-- پاس دادن ViewModel جدید
                                exportViewModel = exportViewModel, // <-- پاس دادن ViewModel جدید
                                diagnosticExportViewModel = diagnosticExportViewModel, // <-- پاس دادن ViewModel جدید
                                navigationViewModel = navigationViewModel, // <-- پاس دادن ViewModel جدید

                                onStartScan = {
                                    deviceInfoViewModel.startScan(this@MainActivity) {
                                        // بعد از اتمام اسکن، به ViewModel ناوبری اطلاع می‌دهیم
                                        navigationViewModel.onScanCompleted()
                                    }
                                }
                            )

                            // دیالوگ پرمیشن‌ها
                            // Permissions dialog
                            if (showPermissionDialog) {
                                AppPermissionsDialog(
                                    permissions = permissions,
                                    onContinue = {
                                        permissionViewModel.dismissPermissionDialog()
                                        permissionViewModel.startPermissionRequest { permission ->
                                            currentRequestedPermission = permission
                                            permissionLauncher.launch(permission)
                                        }
                                    },
                                    onSkip = {
                                        permissionViewModel.skipPermissions()
                                    }
                                )
                            }
                            }
                        }
                    }
                }
            }
        }
    }


}

    override fun onDestroy() {
        super.onDestroy()
        // پاک کردن منابع SplashScreenManager
        // Clean up SplashScreenManager resources
        if (::splashScreenManager.isInitialized) {
            splashScreenManager.cleanup()
        }
        }
    }