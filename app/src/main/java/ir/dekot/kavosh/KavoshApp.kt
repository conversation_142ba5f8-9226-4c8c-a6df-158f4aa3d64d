package ir.dekot.kavosh

import android.app.Application
import coil.ImageLoader
import coil.ImageLoaderFactory
import coil.disk.DiskCache
import coil.memory.MemoryCache
import coil.request.CachePolicy
import coil.util.DebugLogger
import com.google.zxing.client.android.BuildConfig
import dagger.hilt.android.HiltAndroidApp
import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit

/**
 * بهینه‌سازی ۶: کلاس Application سفارشی برای راه‌اندازی Hilt و Coil
 * Optimization 6: Custom Application class for Hilt and Coil setup
 */
@HiltAndroidApp
class KavoshApp : Application(), ImageLoaderFactory {

    override fun onCreate() {
        super.onCreate()
        // بهینه‌سازی ۶: راه‌اندازی Coil با تنظیمات بهینه
        // Optimization 6: Setup Coil with optimized settings
    }

    /**
     * بهینه‌سازی ۶: ایجاد ImageLoader سفارشی با کش بهینه
     * Optimization 6: Create custom ImageLoader with optimized caching
     */
    override fun newImageLoader(): ImageLoader {
        return ImageLoader.Builder(this)
            .memoryCache {
                MemoryCache.Builder(this)
                    .maxSizePercent(0.25) // استفاده از 25% حافظه برای کش تصاویر
                    .build()
            }
            .diskCache {
                DiskCache.Builder()
                    .directory(cacheDir.resolve("image_cache"))
                    .maxSizeBytes(50 * 1024 * 1024) // 50MB کش دیسک
                    .build()
            }
            .okHttpClient {
                OkHttpClient.Builder()
                    .connectTimeout(15, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build()
            }
            .respectCacheHeaders(false) // کنترل کامل کش توسط ما
            .memoryCachePolicy(CachePolicy.ENABLED)
            .diskCachePolicy(CachePolicy.ENABLED)
            .networkCachePolicy(CachePolicy.ENABLED)
            .logger(if (BuildConfig.DEBUG) DebugLogger() else null)
            .build()
    }
}