package ir.dekot.kavosh.feature_dashboard.view

import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import ir.dekot.kavosh.core.navigation.NavigationViewModel
import ir.dekot.kavosh.feature_dashboard.viewModel.DashboardViewModel
import ir.dekot.kavosh.feature_export_and_sharing.viewModel.ExportViewModel
import ir.dekot.kavosh.feature_settings.viewModel.SettingsViewModel
import ir.dekot.kavosh.core.navigation.FloatingBottomNavigation
import ir.dekot.kavosh.core.navigation.BottomNavItem
import ir.dekot.kavosh.feature_settings.view.SettingsScreen
import ir.dekot.kavosh.feature_export_and_sharing.view.ShareScreen
import ir.dekot.kavosh.feature_deviceInfo.model.InfoCategory
import ir.dekot.kavosh.feature_deviceInfo.viewModel.DeviceInfoViewModel
import ir.dekot.kavosh.feature_testing.view.TestsScreen

/**
 * صفحه اصلی جدید با Bottom Navigation
 * شامل چهار تب: اطلاعات، تست‌ها، تنظیمات، اشتراک‌گذاری
 */

@RequiresApi(Build.VERSION_CODES.R)
@Composable
fun MainScreen(
    deviceInfoViewModel: DeviceInfoViewModel,
    settingsViewModel: SettingsViewModel,
    dashboardViewModel: DashboardViewModel,
    exportViewModel: ExportViewModel,
    navigationViewModel: NavigationViewModel,
    onCategoryClick: (InfoCategory, Context) -> Unit,
    onNavigateToAbout: () -> Unit,
    onEditDashboardClick: () -> Unit,
    onCpuStressTestClick: () -> Unit,
    onStorageTestClick: () -> Unit,
    onDisplayTestClick: () -> Unit,
    onNetworkToolsClick: () -> Unit,
    onHealthCheckClick: () -> Unit,
    onPerformanceScoreClick: () -> Unit
) {
    // مدیریت بخش فعلی از NavigationViewModel
    val selectedTab by navigationViewModel.currentBottomNavSection.collectAsState()
    val context = LocalContext.current

    // مدیریت دکمه بازگشت
    BackHandler(enabled = selectedTab != BottomNavItem.INFO) {
        navigationViewModel.setBottomNavSection(BottomNavItem.INFO)
    }

    // حذف bottomBar از Scaffold و استفاده از Box برای navigation شناور
    Scaffold(
        modifier = Modifier.fillMaxSize()
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // محتوای اصلی صفحه
            when (selectedTab) {
                BottomNavItem.INFO -> {
                    DashboardContent(
                        settingsViewModel = settingsViewModel,
                        dashboardViewModel = dashboardViewModel,
                        onCategoryClick = onCategoryClick
                    )
                }

                BottomNavItem.TESTS -> {
                    TestsScreen(
                        onCpuStressTestClick = onCpuStressTestClick,
                        onStorageTestClick = onStorageTestClick,
                        onDisplayTestClick = onDisplayTestClick,
                        onNetworkToolsClick = onNetworkToolsClick,
                        onHealthCheckClick = onHealthCheckClick,
                        onPerformanceScoreClick = onPerformanceScoreClick
                    )
                }

                BottomNavItem.SETTINGS -> {
                    SettingsScreen(
                        viewModel = settingsViewModel,
                        onNavigateToAbout = onNavigateToAbout,
                        onEditDashboardClick = onEditDashboardClick,
                        onBackClick = { /* در MainScreen دکمه back نداریم */ }
                    )
                }

                BottomNavItem.SHARE -> {
                    ShareScreen(
                        exportViewModel = exportViewModel
                    )
                }
            }

            // Navigation Bar شناور در پایین صفحه
            FloatingBottomNavigation(
                selectedItem = selectedTab,
                onItemSelected = { navigationViewModel.setBottomNavSection(it) },
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}
