<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name"><PERSON><PERSON><PERSON></string>
    <string name="app_description">Advanced Device Inspector</string>
    <string name="settings">Settings</string>
    <string name="back">Back</string>
    <string name="choose_app_theme">Choose App Theme</string>
    <string name="system_default">System Default</string>
    <string name="light">Light</string>
    <string name="dark">Dark</string>
    <string name="dashboard_settings">Dashboard Settings</string>
    <string name="enable_item_reordering">Enable Item Reordering</string>
    <string name="language_settings">Language</string>
    <string name="choose_language">Choose app language</string>
    <string name="persian">Persian</string>
    <string name="english">English</string>
    <string name="edit_dashboard_items">Show/Hide Items</string>

    <!-- Theme Descriptions -->
    <string name="theme_system_desc">Follow system settings</string>
    <string name="theme_light_desc">Always light</string>
    <string name="theme_dark_desc">Always dark</string>

    <!-- Language Descriptions -->
    <string name="language_persian_desc">فارسی - Persian language</string>
    <string name="language_english_desc">English - انگلیسی</string>



    <!-- Settings Screen New Strings -->
    <string name="settings_appearance">Appearance</string>
    <string name="settings_appearance_desc">Theme, colors, and language</string>
    <string name="settings_dashboard">Dashboard</string>
    <string name="settings_dashboard_desc">Layout and display options</string>
    <string name="settings_performance">Performance</string>
    <string name="settings_performance_desc">Advanced settings and optimizations</string>
    <string name="settings_about">About</string>
    <string name="settings_about_desc">App information and support</string>
    <string name="theme_selection">Theme Selection</string>
    <string name="language_selection">Language Selection</string>
    <string name="dashboard_customization">Dashboard Customization</string>
    <string name="advanced_settings">Advanced Settings</string>
    <string name="enable_animations">Enable Animations</string>
    <string name="enable_animations_desc">Show smooth transitions and effects</string>
    <string name="auto_refresh">Auto Refresh</string>
    <string name="auto_refresh_desc">Automatically update device information</string>
    <string name="cache_management">Cache Management</string>
    <string name="clear_cache">Clear Cache</string>
    <string name="clear_cache_desc">Clear stored device information</string>
    <string name="cache_cleared">Cache cleared successfully</string>
    <string name="export_settings">Export Settings</string>
    <string name="import_settings">Import Settings</string>
    <string name="reset_settings">Reset to Default</string>
    <string name="reset_settings_desc">Reset all settings to default values</string>
    <string name="settings_reset">Settings reset successfully</string>
    <string name="confirm_clear_cache">Are you sure you want to clear all cached data?</string>
    <string name="confirm_reset_settings">Are you sure you want to reset all settings to default?</string>
    <string name="action_confirm">Confirm</string>

    <!-- Bottom Navigation -->
    <string name="nav_dashboard">Dashboard</string>
    <string name="nav_dashboard_desc">Device information and specifications</string>
    <string name="nav_tests">Tests</string>
    <string name="nav_tests_desc">Hardware testing tools</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_settings_desc">App settings and preferences</string>
    <string name="nav_share">Share</string>
    <string name="nav_share_desc">Export and share device information</string>

    <!-- Tests Screen -->
    <string name="tests_title">Hardware Tests</string>
    <string name="tests_subtitle">Test your device performance</string>
    <string name="test_cpu_stress">CPU Stress Test</string>
    <string name="test_cpu_stress_desc">Test CPU performance under load</string>
    <string name="test_storage_speed">Storage Speed Test</string>
    <string name="test_storage_speed_desc">Measure read/write speeds</string>
    <string name="test_display">Display Test</string>
    <string name="test_display_desc">Test screen colors and pixels</string>
    <string name="test_network_tools">Network Tools</string>
    <string name="test_network_tools_desc">Wi-Fi scanner and network utilities</string>
    <string name="test_health_check">Health Check</string>
    <string name="test_health_check_desc">Comprehensive device health analysis</string>
    <string name="test_performance_score">Performance Score</string>
    <string name="test_performance_score_desc">Benchmark your device performance</string>



    <!-- Health Check -->
    <string name="health_check_title">Device Health Check</string>
    <string name="health_check_subtitle">Comprehensive analysis of your device</string>
    <string name="health_check_performance">Performance</string>
    <string name="health_check_performance_desc">Overall system performance</string>
    <string name="health_check_performance_rec">Close background apps to improve performance</string>
    <string name="health_check_storage">Storage</string>
    <string name="health_check_storage_desc">%d%% free space available</string>
    <string name="health_check_storage_rec">Free up storage space by deleting unused files</string>
    <string name="health_check_battery">Battery</string>
    <string name="health_check_battery_desc">Battery health: %s</string>
    <string name="health_check_battery_rec">Consider battery optimization settings</string>
    <string name="health_check_temperature">Temperature</string>
    <string name="health_check_temperature_desc">Device thermal status</string>
    <string name="health_check_temperature_rec">Let device cool down if overheating</string>
    <string name="health_check_memory">Memory</string>
    <string name="health_check_memory_desc">%d%% RAM available</string>
    <string name="health_check_memory_rec">Close unused apps to free up memory</string>
    <string name="health_check_network">Network</string>
    <string name="health_check_network_desc">Network connectivity status</string>
    <string name="health_check_security">Security</string>
    <string name="health_check_security_desc">System security status</string>
    <string name="health_check_security_rec">Keep your system updated for security</string>
    <string name="health_check_system">System</string>
    <string name="health_check_system_desc">Android %s system status</string>
    <string name="health_status_excellent">Excellent</string>
    <string name="health_status_good">Good</string>
    <string name="health_status_fair">Fair</string>
    <string name="health_status_poor">Poor</string>
    <string name="health_status_critical">Critical</string>
    <string name="health_check_start_test">Start Health Check</string>
    <string name="health_check_analyzing">Analyzing device health…</string>
    <string name="health_check_overall_health">Overall Device Health</string>
    <string name="health_check_recommendations_title">Recommendations</string>
    <string name="health_check_history_title">Test History</string>
    <string name="health_check_no_history">No previous tests found</string>
    <string name="health_check_export_report">Export Report</string>
    <string name="health_check_test_date">Test Date: %s</string>
    <string name="health_check_expand_details">Tap to view details</string>
    <string name="health_check_collapse_details">Tap to collapse</string>

    <!-- Performance Score -->
    <string name="performance_score_title">Performance Score</string>
    <string name="performance_score_subtitle">Benchmark your device performance</string>
    <string name="performance_score_start_test">Start Performance Test</string>
    <string name="performance_score_running_benchmarks">Running Benchmarks…</string>
    <string name="performance_score_this_may_take">This may take a few minutes</string>
    <string name="performance_score_overall_score">Performance Score</string>
    <string name="performance_score_global_ranking">Global Ranking</string>
    <string name="performance_score_benchmark_results">Benchmark Results</string>
    <string name="performance_grade_s_plus">S+</string>
    <string name="performance_grade_s">S</string>
    <string name="performance_grade_a_plus">A+</string>
    <string name="performance_grade_a">A</string>
    <string name="performance_grade_b_plus">B+</string>
    <string name="performance_grade_b">B</string>
    <string name="performance_grade_c_plus">C+</string>
    <string name="performance_grade_c">C</string>
    <string name="performance_grade_d">D</string>
    <string name="performance_grade_f">F</string>
    <string name="performance_category_cpu">CPU</string>
    <string name="performance_category_gpu">GPU</string>
    <string name="performance_category_ram">RAM</string>
    <string name="performance_category_storage">Storage</string>
    <string name="performance_category_network">Network</string>
    <string name="performance_category_battery">Battery</string>
    <string name="performance_category_thermal">Thermal</string>


    <string name="comparison_recommendation">Recommendation</string>
    <string name="comparison_overall_comparison">Overall Comparison</string>
    <string name="comparison_compared_devices">Compared Devices</string>
    <string name="comparison_history_title">Comparison History</string>
    <string name="comparison_no_history">No previous comparisons found</string>
    <string name="comparison_export_report">Export Comparison</string>
    <string name="comparison_test_date">Comparison Date: %s</string>
    <string name="comparison_expand_details">Tap to view details</string>
    <string name="comparison_collapse_details">Tap to collapse</string>

    <!-- Share Screen -->
    <string name="share_title">Export &amp; Share</string>
    <string name="share_subtitle">Save or share your device information</string>
    <string name="export_format_txt">Text Format (TXT)</string>
    <string name="export_format_pdf">PDF Document</string>
    <string name="export_format_json">JSON Data</string>
    <string name="export_format_html">HTML Report</string>
    <string name="export_format_excel">Excel File (XLSX)</string>
    <string name="export_format_qr_code">QR Code</string>
    <string name="quick_share">Quick Share</string>
    <string name="quick_share_desc">Share basic device info as text or QR Code</string>
    <string name="full_report">Full Report</string>
    <string name="full_report_desc">Complete device specifications</string>

    <string name="scan_device">Scan Device</string>
    <string name="ready_to_scan">Ready to scan…</string>
    <string name="copy">Copy</string>
    <string name="share">Share</string>
    <string name="cancel">Cancel</string>
    <string name="copied_to_clipboard">Copied to clipboard</string>
    <string name="copy_selection_title">Select items to copy</string>
    <string name="copy_selection_button">Copy Selected</string>
    <string name="share_selection_title">Select items to share</string>
    <string name="share_selection_button">Share Selected</string>
    <string name="share_via">Share via…</string>
    <string name="full_report_title">Full Device Specification Report</string>
    <string name="file_exported_successfully">File saved successfully to your chosen path.</string>
    <string name="file_export_failed">Error creating file. Please try again.</string>
    <string name="category_soc">Hardware</string>
    <string name="category_device">Device</string>
    <string name="category_system">System</string>
    <string name="category_battery">Battery</string>
    <string name="category_sensors">Sensors</string>
    <string name="category_thermal">Thermal</string>
    <string name="category_network">Network</string>
    <string name="category_camera">Camera</string>
    <string name="label_undefined">Undefined</string>
    <string name="label_rooted">Rooted</string>
    <string name="label_not_rooted">Not Rooted</string>
    <string name="label_yes">Yes</string>
    <string name="label_no">No</string>
    <string name="label_on">On</string>
    <string name="label_off">Off</string>
    <string name="label_disconnected">Disconnected</string>
    <string name="label_not_available">N/A</string>
    <string name="label_permission_required">Permission Required</string>
    <string name="label_sleeping">Sleeping</string>
    <string name="unit_format_celsius">%.1f °C</string>
    <string name="unit_format_volt">%.2f V</string>
    <string name="unit_format_percent">%d%%</string>
    <string name="unit_format_mhz">%d MHz</string>
    <string name="unit_format_ghz">%.2f GHz</string>
    <string name="unit_format_mm">%.2fmm</string>
    <string name="unit_format_mm_area">%.2f x %.2f mm</string>
    <string name="unit_format_size_or_speed">%.1f %s</string>
    <string name="unit_format_mp">%d MP</string>
    <string name="battery_health">Health</string>
    <string name="battery_level">Charge Level</string>
    <string name="battery_status">Status</string>
    <string name="battery_technology">Technology</string>
    <string name="battery_temperature">Battery Temperature</string>
    <string name="battery_voltage">Voltage</string>
    <string name="battery_health_good">Good</string>
    <string name="battery_health_dead">Dead</string>
    <string name="battery_health_overheat">Overheat</string>
    <string name="battery_health_over_voltage">Over Voltage</string>
    <string name="battery_health_unspecified_failure">Unspecified Failure</string>
    <string name="battery_status_charging">Charging</string>
    <string name="battery_status_discharging">Discharging</string>
    <string name="battery_status_full">Full</string>
    <string name="battery_status_not_charging">Not Charging</string>
    <string name="cpu_title">CPU</string>
    <string name="cpu_model">Model</string>
    <string name="cpu_topology">Topology</string>
    <string name="cpu_process">Process</string>
    <string name="cpu_architecture">Architecture</string>
    <string name="cpu_core_count">Core Count</string>
    <string name="cpu_live_speed">Live Core Speed</string>
    <string name="cpu_clock_range">Clock Speed Range</string>
    <string name="cpu_core_prefix">Core %d</string>
    <string name="gpu_title">GPU</string>
    <string name="gpu_model">Model</string>
    <string name="gpu_vendor">Vendor</string>
    <string name="gpu_load">GPU Load</string>
    <string name="ram_title">RAM</string>
    <string name="ram_total">Total Memory</string>
    <string name="ram_available">Available Memory</string>
    <string name="system_info_title">System Information</string>
    <string name="system_android_version">Android Version</string>
    <string name="system_sdk_level">API Level</string>
    <string name="system_build_number">Build Number</string>
    <string name="system_root_status">Root Status</string>
    <string name="display_title">Display</string>
    <string name="display_resolution">Resolution</string>
    <string name="display_density">Density</string>
    <string name="display_refresh_rate">Refresh Rate</string>
    <string name="storage_title">Internal Storage</string>
    <string name="storage_total">Total Space</string>
    <string name="storage_available">Available Space</string>
    <string name="network_info_title">Network Information</string>
    <string name="network_download_speed">Download Speed</string>
    <string name="network_upload_speed">Upload Speed</string>
    <string name="network_hotspot_status">Hotspot Status</string>
    <string name="network_connection_type">Connection Type</string>
    <string name="network_ipv4">IPv4 Address</string>
    <string name="network_ipv6">IPv6 Address</string>
    <string name="network_wifi_details">Wi-Fi Details</string>
    <string name="network_ssid">SSID</string>
    <string name="network_bssid">BSSID</string>
    <string name="network_signal_strength">Signal Strength</string>
    <string name="network_link_speed">Link Speed</string>
    <string name="network_dns1">DNS 1</string>
    <string name="network_dns2">DNS 2</string>
    <string name="network_mobile_details">Mobile Network Details</string>
    <string name="network_operator">Network Operator</string>
    <string name="camera_megapixels">Megapixels</string>
    <string name="camera_max_resolution">Max Resolution</string>
    <string name="camera_flash_support">Flash Support</string>
    <string name="camera_apertures">Apertures</string>
    <string name="camera_focal_lengths">Focal Lengths</string>
    <string name="camera_sensor_size">Sensor Size</string>
    <string name="camera_facing_front">Front Camera</string>
    <string name="camera_facing_back">Back Camera</string>
    <string name="camera_facing_external">External Camera</string>
    <string name="camera_facing_unknown">Unknown Camera</string>
    <string name="sensor_vendor">Vendor: %s</string>
    <string name="sensor_details_title">Sensor Details</string>
    <string name="sensor_testable">Testable</string>
    <string name="sensor_not_testable">Not Testable</string>
    <string name="sensor_start_test">Start Test</string>
    <string name="no_sensors_found">No sensors found on this device.</string>
    <string name="about_title">About</string>
    <string name="about_version">Version %s</string>
    <string name="about_description">Kavosh is an application designed to provide detailed hardware and software information about your device.</string>
    <string name="about_developer">Developed by %s</string>
    <string name="storage_speed_test_title">Storage Speed Test</string>
    <string name="storage_speed_test_description">This test measures the sequential read and write speed of your internal storage by creating and then deleting a temporary 100MB file.</string>
    <string name="storage_speed_write">Write Speed</string>
    <string name="storage_speed_read">Read Speed</string>
    <string name="storage_speed_test_button">Start Test</string>
    <string name="testing">Testing…</string>
    <string name="label_error">Error</string>
    <string name="sensor_test_button">Test</string>
    <string name="unknown_sensor">Unknown Sensor</string>
    <string name="sensor_no_live_view">Live view is not available for this sensor.</string>
    <string name="illuminance">Illuminance</string>
    <string name="unit_format_lux">%.1f lx</string>
    <string name="accelerometer_title">Acceleration Force</string>
    <string name="accelerometer_unit">(including gravity) in m/s²</string>
    <string name="compass_bearing">Bearing</string>
    <string name="compass_n">N</string>
    <string name="compass_ne">NE</string>
    <string name="compass_e">E</string>
    <string name="compass_se">SE</string>
    <string name="compass_s">S</string>
    <string name="compass_sw">SW</string>
    <string name="compass_w">W</string>
    <string name="compass_nw">NW</string>
    <string name="gyroscope_title">Rate of Rotation</string>
    <string name="gyroscope_unit">(in rad/s)</string>
    <string name="proximity_near">Object is Near</string>
    <string name="proximity_far">Object is Far</string>
    <string name="unit_format_cm">%.1f cm</string>
    <string name="barometer_title">Atmospheric Pressure</string>
    <string name="unit_format_hpa">%.2f hPa</string>
    <string name="gravity_title">Gravity Force</string>
    <string name="linear_acceleration_title">Linear Acceleration</string>
    <string name="step_counter_title">Steps Since Reboot</string>
    <string name="ambient_temperature_title">Ambient Temperature</string>
    <string name="relative_humidity_title">Relative Humidity</string>
    <string name="unit_format_percent_relative">%.1f %%</string>
    <string name="uncalibrated_accelerometer_title">Uncalibrated Accelerometer</string>
    <string name="uncalibrated_gyroscope_title">Uncalibrated Gyroscope</string>
    <string name="uncalibrated_magnetometer_title">Uncalibrated Magnetometer</string>
    <string name="magnetometer_unit">(in μT)</string>
    <string name="uncalibrated_raw_data">Raw Data</string>
    <string name="uncalibrated_bias">Estimated Bias</string>
    <string name="step_detector_title">Step Detected</string>
    <string name="step_detector_description">Counts each step as a separate event.</string>
    <string name="tilt_detector_waiting">Waiting for a tilt event…</string>
    <string name="tilt_detector_detected">Tilt Detected!</string>
    <string name="significant_motion_title">Significant Motion</string>
    <string name="pick_up_gesture_title">Pick-up Gesture</string>
    <string name="tilt_detector_title">Tilt Detector</string>
    <string name="trigger_sensor_waiting">Waiting for event…</string>
    <string name="trigger_sensor_detected">Event Detected!</string>
    <string name="rotation_vector_title">Device Orientation</string>
    <string name="rotation_azimuth">Azimuth</string>
    <string name="rotation_pitch">Pitch</string>
    <string name="rotation_roll">Roll</string>
    <string name="orientation_angles">Orientation Angles</string>
    <string name="raw_sensor_data">Raw Sensor Data</string>
    <string name="live_chart_title">Live Data Chart</string>
    <string name="live_values_title">Live Vector Values</string>
    <string name="display_test_title">Display Test</string>
    <string name="display_test_description">Tools to check the health and quality of your screen.</string>
    <string name="dead_pixel_test">Dead Pixel Test</string>
    <string name="color_banding_test">Color Banding Test</string>
    <string name="dead_pixel_instruction">Tap anywhere on the screen to cycle through colors (Black, White, Red, Green, Blue). Look for any pixels that don\'t match the current color.</string>
    <string name="grayscale_gradient">Grayscale</string>
    <string name="red_gradient">Red</string>
    <string name="green_gradient">Green</string>
    <string name="blue_gradient">Blue</string>
    <string name="category_sim">SIM Card</string>
    <string name="sim_slot_index">SIM Slot %d</string>
    <string name="sim_carrier">Carrier</string>
    <string name="sim_country_iso">Country (ISO)</string>
    <string name="sim_network_code">Network Code (MNC)</string>

    <string name="sim_is_roaming">Network Roaming</string>
    <string name="sim_data_roaming">Data Roaming</string>
    <string name="permission_required_sim">READ_PHONE_STATE permission is required to access SIM card information.</string>
    <string name="battery_health_details">Health and Capacity</string>
    <string name="battery_charge_stats">Charging / Discharging</string>
    <string name="battery_capacity_design">Design Capacity</string>
    <string name="battery_capacity_actual">Actual Capacity (Estimated)</string>
    <string name="battery_health_estimated">Estimated Health</string>
    <string name="battery_charge_current">Current (Now)</string>
    <string name="battery_charge_power">Power (Now)</string>
    <string name="battery_unit_mah">%d mAh</string>
    <string name="battery_unit_ma">%d mA</string>
    <string name="battery_unit_watt">%.2f W</string>
    <string name="category_apps">Apps</string>
    <string name="apps_system_apps">System Apps</string>
    <string name="apps_user_apps">User Apps</string>
    <string name="app_version">Version: %1$s (%2$d)</string>
    <string name="app_installed_on">Installed on: %s</string>
    <string name="app_permissions">Permissions</string>
    <string name="app_no_permissions">No permissions requested.</string>

    <!-- CPU Stress Test -->
    <string name="cpu_stress_test_title">CPU Stress Test</string>
    <string name="start_test">Start Test</string>
    <string name="stop_test">Stop Test</string>

    <!-- CPU Stress Test - Advanced -->
    <string name="cpu_usage">CPU Usage</string>
    <string name="cpu_temperature">CPU Temperature</string>
    <string name="power_consumption">Power Consumption</string>
    <string name="performance_score">Performance Score</string>
    <string name="stability_score">Stability Score</string>
    <string name="test_preparing">Preparing test…</string>
    <string name="test_running">Test running…</string>
    <string name="test_stopping">Stopping test…</string>
    <string name="test_completed">Test completed</string>
    <string name="test_error">Test error</string>
    <string name="emergency_stop_temperature">Emergency stop: High temperature</string>
    <string name="throttling_detected">Throttling detected</string>
    <string name="thermal_status_normal">Normal</string>
    <string name="thermal_status_warm">Warm</string>
    <string name="thermal_status_hot">Hot</string>
    <string name="thermal_status_critical">Critical</string>
    <string name="thermal_status_unknown">Unknown</string>

    <!-- Test Types -->
    <string name="test_type_single_core">Single Core</string>
    <string name="test_type_multi_core">Multi Core</string>
    <string name="test_type_stability">Stability</string>
    <string name="test_type_peak_performance">Peak Performance</string>

    <!-- Test Intensity -->
    <string name="intensity_low">Low</string>
    <string name="intensity_medium">Medium</string>
    <string name="intensity_high">High</string>
    <string name="intensity_extreme">Extreme</string>

    <!-- Performance Patterns -->
    <string name="pattern_thermal_throttling">Thermal Throttling</string>
    <string name="pattern_stable_performance">Stable Performance</string>
    <string name="pattern_high_variation">High Variation</string>
    <string name="pattern_optimal_performance">Optimal Performance</string>

    <!-- Network Tools -->
    <string name="network_tools_title">Network Tools</string>
    <string name="wifi_scanner_tab">Wi-Fi Scanner</string>
    <string name="ping_tool_tab">Ping Tool</string>
    <string name="location_permission_required">Location permission is required to scan for Wi-Fi networks.</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="host_or_ip_address">Host or IP Address</string>
    <string name="start_stop_ping">Start/Stop Ping</string>

    <!-- WiFi Scanner -->
    <string name="wifi_permission_required_title">Location Permission Required</string>
    <string name="wifi_permission_required_desc">Wi-Fi scanning requires location permission to function properly.</string>
    <string name="location_disabled_title">Location Services Disabled</string>
    <string name="location_disabled_desc">Please enable location services to scan for Wi-Fi networks.</string>
    <string name="enable_location">Enable Location</string>
    <string name="no_wifi_networks_found">No Wi-Fi networks found</string>
    <string name="tap_to_scan">Tap the scan button to search for networks</string>
    <string name="scan_wifi">Scan Wi-Fi</string>
    <string name="wifi_network_item">%1$s - %2$s</string>
    <string name="wifi_signal_strength">Signal: %d dBm</string>
    <string name="wifi_frequency">Frequency: %d MHz</string>
    <string name="wifi_security">Security: %s</string>
    <string name="wifi_capabilities">Capabilities: %s</string>

    <!-- WiFi Scanner Additional -->
    <string name="refresh_scan">Refresh Scan</string>
    <string name="location_off">Location Off</string>
    <string name="location_service_disabled">Location Service Is Disabled</string>
    <string name="turn_on_location_for_wifi">Please turn on your device\'s location to allow Wi-Fi scanning.</string>
    <string name="open_location_settings">Open Location Settings</string>
    <string name="press_refresh_to_scan">Press the refresh button to scan for nearby networks.</string>
    <string name="signal_strength">Signal Strength</string>
    <string name="wifi_dbm_format">%d dBm</string>
    <string name="wifi_mhz_format">%d MHz</string>

    <!-- Storage Test -->
    <string name="storage_test_title">Storage Speed Test</string>
    <string name="storage_test_description">This test measures the read and write speed of your device\'s internal storage.</string>
    <string name="test_results">Test Results</string>
    <string name="read_speed">Read Speed</string>
    <string name="write_speed">Write Speed</string>
    <string name="test_progress">Test Progress</string>
    <string name="start_test_button">Start Test</string>
    <string name="testing_in_progress">Testing…</string>
    <string name="retest">Retest</string>
    <string name="retry">Retry</string>

    <!-- Enhanced Storage Speed Test -->
    <string name="storage_speed_permission_title">Storage Speed Test Permission</string>
    <string name="storage_speed_permission_message">This test will create a temporary 1GB file to accurately measure your storage speed. The file will be automatically deleted after the test.\n\nDo you want to proceed?</string>
    <string name="storage_speed_permission_grant">Start Test</string>
    <string name="storage_speed_permission_deny">Cancel</string>
    <string name="storage_speed_live_chart_title">Live Speed Monitor</string>
    <string name="storage_speed_write_chart_title">Write Speed Chart</string>
    <string name="storage_speed_read_chart_title">Read Speed Chart</string>
    <string name="storage_speed_test_duration">Test Duration: %d seconds</string>
    <string name="storage_speed_file_size">Test File Size: 1GB</string>
    <string name="storage_speed_history_title">Test History</string>
    <string name="storage_speed_no_history">No previous tests found</string>
    <string name="storage_speed_export_title">Export Test Results</string>
    <string name="storage_speed_test_completed">Test completed successfully</string>
    <string name="storage_speed_test_failed">Test failed. Please try again.</string>
    <string name="storage_speed_insufficient_space">Insufficient storage space for test</string>
    <string name="storage_speed_creating_file">Creating test file…</string>
    <string name="storage_speed_testing_write">Testing write speed…</string>
    <string name="storage_speed_testing_read">Testing read speed…</string>
    <string name="storage_speed_cleaning_up">Cleaning up…</string>



    <string name="test_controls">Test Controls</string>
    <string name="quick_test">Quick Test</string>
    <string name="last_test_result">Last Test Result</string>
    <string name="test_history">Test History</string>
    <string name="expand">Expand</string>
    <string name="collapse">Collapse</string>
    <string name="clear">Clear</string>
    <string name="ram_test_permission_title">RAM Test Permission</string>
    <string name="ram_test_permission_message">This test will allocate memory blocks to measure RAM performance. It may temporarily use significant memory resources. Continue?</string>

    <!-- Compass Directions -->
    <string name="compass_north">N</string>
    <string name="compass_east">E</string>
    <string name="compass_south">S</string>
    <string name="compass_west">W</string>

    <!-- Share Screen -->
    <string name="save_device_info">Save Device Information</string>

    <!-- Color Themes -->
    <string name="color_theme_selection">Color Theme</string>
    <string name="color_theme_blue">Blue</string>
    <string name="color_theme_green">Green</string>
    <string name="color_theme_red">Red</string>
    <string name="color_theme_purple">Purple</string>
    <string name="color_theme_orange">Orange</string>
    <string name="color_theme_teal">Teal</string>
    <string name="color_theme_indigo">Indigo</string>
    <string name="color_theme_pink">Pink</string>
    <string name="color_theme_custom">Custom</string>
    <string name="custom_color_picker">Custom Color Picker</string>
    <string name="select_primary_color">Select Primary Color</string>
    <string name="preview">Preview</string>
    <string name="apply_color">Apply Color</string>
    <string name="reset_to_default">Reset to Default</string>
    <string name="advanced_color_picker">Advanced</string>
    <string name="quick_color_picker">Quick</string>

    <!-- General UI -->
    <string name="back_button">Back</string>
    <string name="running">Running…</string>
    <string name="error_prefix">Error:</string>

    <!-- App Permissions Dialog -->
    <string name="app_permissions_dialog_title">App Permissions</string>
    <string name="app_permissions_dialog_subtitle">Kavosh needs the following permissions to provide complete device information</string>
    <string name="app_permissions_dialog_continue">Continue</string>
    <string name="app_permissions_dialog_skip">Skip for Now</string>

    <!-- Permission Items -->
    <string name="permission_phone_state_title">Phone State</string>
    <string name="permission_phone_state_description">Access SIM card and network information</string>

    <string name="permission_location_title">Location</string>
    <string name="permission_location_description">Scan Wi-Fi networks and location-based features</string>

    <string name="permission_camera_title">Camera</string>
    <string name="permission_camera_description">Test camera functionality and QR code features</string>

    <string name="permission_wifi_state_title">Wi-Fi State</string>
    <string name="permission_wifi_state_description">Monitor Wi-Fi connection and network details</string>

    <string name="permission_network_state_title">Network State</string>
    <string name="permission_network_state_description">Monitor network connectivity and status</string>

    <!-- Enhanced Temperature Sensors -->
    <string name="thermal_sensor_battery">Battery Temperature</string>
    <string name="thermal_sensor_cpu">CPU Temperature</string>
    <string name="thermal_sensor_gpu">GPU Temperature</string>
    <string name="thermal_sensor_ambient">Ambient Temperature</string>

</resources>