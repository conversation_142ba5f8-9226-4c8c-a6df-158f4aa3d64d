<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Kavosh" parent="android:Theme.Material.Light.NoActionBar" />

    <!-- Traditional Splash Screen Theme for Android 11 and below -->
    <style name="Theme.Kavosh.SplashScreen.Traditional" parent="Theme.Kavosh">
        <!-- Set the splash screen background -->
        <item name="android:windowBackground">@drawable/splash_screen_traditional</item>
        <!-- Remove action bar -->
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <!-- Make it fullscreen -->
        <item name="android:windowFullscreen">true</item>
        <!-- Prevent window animation -->
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <!-- Modern Splash Screen Theme for Android 12+ -->
    <style name="Theme.Kavosh.SplashScreen" parent="Theme.SplashScreen">
        <!-- Set the splash screen background color for light theme -->
        <item name="android:windowSplashScreenBackground">@color/splash_background_light</item>

        <!-- Set the animated icon for the splash screen -->
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/my_animation_avd</item>

        <!-- Set the duration of the animated icon -->
        <item name="android:windowSplashScreenAnimationDuration">4233</item>

        <!-- Set the icon background color (transparent for clean look) -->
        <item name="android:windowSplashScreenIconBackgroundColor">@android:color/transparent</item>

        <item name="android:windowSplashScreenBrandingImage">@drawable/branding2</item>

        <!-- Set the theme to use after splash screen -->
        <item name="postSplashScreenTheme">@style/Theme.Kavosh</item>
    </style>
</resources>