<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
                 xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector
            android:width="1080dp"
            android:height="1080dp"
            android:viewportWidth="1080"
            android:viewportHeight="1080">

            <group
                android:name="_R_G"
                android:scaleX="0.65"
                android:scaleY="0.65"
                android:pivotX="540"
                android:pivotY="540">

                <group
                    android:name="_R_G_L_6_G_N_6_T_0"
                    android:translateX="479"
                    android:translateY="462.5">
                    <group
                        android:name="_R_G_L_6_G"
                        android:pivotX="-168"
                        android:pivotY="-10"
                        android:scaleX="0.92"
                        android:scaleY="0.92"
                        android:translateX="447"
                        android:translateY="59.5">
                        <path
                            android:name="_R_G_L_6_G_D_0_P_0"
                            android:fillAlpha="1"
                            android:fillColor="#ffffff"
                            android:fillType="nonZero"
                            android:pathData=" M-168 -290 C-13.47,-290 112,-164.53 112,-10 C112,144.53 -13.47,270 -168,270 C-322.53,270 -448,144.53 -448,-10 C-448,-164.53 -322.53,-290 -168,-290c " />
                        <path
                            android:name="_R_G_L_6_G_D_1_P_0"
                            android:pathData=" M-168 -290 C-13.47,-290 112,-164.53 112,-10 C112,144.53 -13.47,270 -168,270 C-322.53,270 -448,144.53 -448,-10 C-448,-164.53 -322.53,-290 -168,-290c "
                            android:strokeAlpha="1"
                            android:strokeColor="#0b0b0b"
                            android:strokeLineCap="round"
                            android:strokeLineJoin="round"
                            android:strokeWidth="13" />
                    </group>
                </group>
                <group
                    android:name="_R_G_L_5_G_N_6_T_0"
                    android:translateX="479"
                    android:translateY="462.5">
                    <group
                        android:name="_R_G_L_5_G"
                        android:pivotX="-168"
                        android:pivotY="-10"
                        android:scaleX="0.92"
                        android:scaleY="0.92"
                        android:translateX="9"
                        android:translateY="57.5">
                        <path
                            android:name="_R_G_L_5_G_D_0_P_0"
                            android:fillAlpha="1"
                            android:fillColor="#ffffff"
                            android:fillType="nonZero"
                            android:pathData=" M-168 -290 C-13.47,-290 112,-164.53 112,-10 C112,144.53 -13.47,270 -168,270 C-322.53,270 -448,144.53 -448,-10 C-448,-164.53 -322.53,-290 -168,-290c " />
                        <path
                            android:name="_R_G_L_5_G_D_1_P_0"
                            android:pathData=" M-168 -290 C-13.47,-290 112,-164.53 112,-10 C112,144.53 -13.47,270 -168,270 C-322.53,270 -448,144.53 -448,-10 C-448,-164.53 -322.53,-290 -168,-290c "
                            android:strokeAlpha="1"
                            android:strokeColor="#0b0b0b"
                            android:strokeLineCap="round"
                            android:strokeLineJoin="round"
                            android:strokeWidth="13" />
                    </group>
                </group>
                <group
                    android:name="_R_G_L_4_G_N_6_T_0"
                    android:translateX="479"
                    android:translateY="462.5">
                    <group
                        android:name="_R_G_L_4_G_T_1"
                        android:scaleX="0.17"
                        android:scaleY="0.17"
                        android:translateX="-322"
                        android:translateY="134.5">
                        <group
                            android:name="_R_G_L_4_G"
                            android:translateX="168"
                            android:translateY="10">
                            <path
                                android:name="_R_G_L_4_G_D_0_P_0"
                                android:fillAlpha="1"
                                android:fillColor="#000000"
                                android:fillType="nonZero"
                                android:pathData=" M-168 -290 C-13.47,-290 112,-164.53 112,-10 C112,144.53 -13.47,270 -168,270 C-322.53,270 -448,144.53 -448,-10 C-448,-164.53 -322.53,-290 -168,-290c " />
                            <path
                                android:name="_R_G_L_4_G_D_1_P_0"
                                android:pathData=" M-168 -290 C-13.47,-290 112,-164.53 112,-10 C112,144.53 -13.47,270 -168,270 C-322.53,270 -448,144.53 -448,-10 C-448,-164.53 -322.53,-290 -168,-290c "
                                android:strokeAlpha="1"
                                android:strokeColor="#0b0b0b"
                                android:strokeLineCap="round"
                                android:strokeLineJoin="round"
                                android:strokeWidth="0" />
                        </group>
                    </group>
                </group>
                <group
                    android:name="_R_G_L_3_G_N_7_T_0"
                    android:translateX="544"
                    android:translateY="537">
                    <group
                        android:name="_R_G_L_3_G_T_1"
                        android:scaleX="0.17"
                        android:scaleY="0.17"
                        android:translateX="60"
                        android:translateY="60">
                        <group
                            android:name="_R_G_L_3_G"
                            android:translateX="168"
                            android:translateY="10">
                            <path
                                android:name="_R_G_L_3_G_D_0_P_0"
                                android:fillAlpha="1"
                                android:fillColor="#000000"
                                android:fillType="nonZero"
                                android:pathData=" M-168 -290 C-13.47,-290 112,-164.53 112,-10 C112,144.53 -13.47,270 -168,270 C-322.53,270 -448,144.53 -448,-10 C-448,-164.53 -322.53,-290 -168,-290c " />
                            <path
                                android:name="_R_G_L_3_G_D_1_P_0"
                                android:pathData=" M-168 -290 C-13.47,-290 112,-164.53 112,-10 C112,144.53 -13.47,270 -168,270 C-322.53,270 -448,144.53 -448,-10 C-448,-164.53 -322.53,-290 -168,-290c "
                                android:strokeAlpha="1"
                                android:strokeColor="#0b0b0b"
                                android:strokeLineCap="round"
                                android:strokeLineJoin="round"
                                android:strokeWidth="0" />
                        </group>
                    </group>
                </group>
                <group
                    android:name="_R_G_L_2_G"
                    android:translateX="518"
                    android:translateY="424">
                    <path
                        android:name="_R_G_L_2_G_D_0_P_0"
                        android:pathData=" M476 460 C476,460 476,488 476,488 C476,488 -432,488 -432,488 C-432,488 -432,460 -432,460 C-432,460 476,460 476,460c "
                        android:strokeAlpha="1"
                        android:strokeColor="#ffffff"
                        android:strokeLineCap="round"
                        android:strokeLineJoin="round"
                        android:strokeWidth="2" />
                </group>
                <group
                    android:name="_R_G_L_1_G"
                    android:translateX="540"
                    android:translateY="540" />
                <group
                    android:name="_R_G_L_0_G"
                    android:translateX="540"
                    android:translateY="540">
                    <path
                        android:name="_R_G_L_0_G_D_0_P_0"
                        android:pathData=" M-443 358 C-443,358 445,358 445,358 "
                        android:strokeAlpha="1"
                        android:strokeColor="#ffffff"
                        android:strokeLineCap="round"
                        android:strokeLineJoin="round"
                        android:strokeWidth="14"
                        android:trimPathEnd="0"
                        android:trimPathOffset="0"
                        android:trimPathStart="0" />
                </group>

            </group>
            <group android:name="time_group" />
        </vector>
    </aapt:attr>
    <target android:name="_R_G_L_4_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="4233"
                    android:pathData="M -322,134.5C -292.293,194.074 -93.7,245.769 -8,138.5"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="4233"
                    android:pathData="M 60,60C 89.707,119.574 288.3,171.269 374,64"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="4233"
                    android:propertyName="trimPathEnd"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="time_group">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="5000"
                    android:propertyName="translateX"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
</animated-vector>