<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Override splash screen theme for Android 11+ (API 30+) -->
    <style name="Theme.Kavosh.SplashScreen.Traditional" parent="Theme.SplashScreen">
        <!-- Set the splash screen background color for light theme -->
        <item name="android:windowSplashScreenBackground">@color/splash_background_light</item>

        <!-- Set the animated icon for the splash screen -->
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/my_animation_avd</item>

        <!-- Set the duration of the animated icon -->
        <item name="android:windowSplashScreenAnimationDuration">4233</item>

        <!-- Set the icon background color (transparent for clean look) -->
        <item name="android:windowSplashScreenIconBackgroundColor">@android:color/transparent</item>

        <item name="android:windowSplashScreenBrandingImage">@drawable/branding2</item>

        <!-- Set the theme to use after splash screen -->
        <item name="postSplashScreenTheme">@style/Theme.Kavosh</item>
    </style>
</resources>
