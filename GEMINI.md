##Project Overview
The "Kavosh" project is an open-source Android application designed to display detailed hardware and software information about the user's device. The application allows users to access details about their CPU, GPU, RAM, battery status, sensors, camera, and network. It also provides testing tools such as a storage speed test, CPU stress test, and display test.

##Architecture and Technologies:

-Architecture: The project follows the MVVM (Model-View-ViewModel) architecture.

-User Interface (UI): Built entirely with Jetpack Compose in a declarative style.

-Dependency Injection (DI): Uses Hilt to manage dependencies throughout the application.

-Concurrency: Utilizes Kotlin Coroutines and Flow to manage asynchronous operations and streaming data.

-Navigation: A custom navigation system is implemented using a NavigationViewModel and a Screen sealed class.

##Language Management:

-To support both Persian and English, always use the strings.xml files located in the res/values (for English) and res/values-fa (for Persian) directories.

Avoid hardcoding strings in the code (e.g., "My Text"). Instead, use stringResource(R.string.string_name).

-Commenting:

Along with the code you write, always add clear and meaningful comments in Persian (Farsi) to make the code easier to understand in the future.

-Changelog:

With every change you make to the project, update the CHANGELOG.md file in the project root.

Versioning should follow the Semantic Versioning standard (MAJOR.MINOR.PATCH).

PATCH: For minor bug fixes.

MINOR: For new features that are backward-compatible.

MAJOR: For major changes that are not backward-compatible.

-State Management:

UI-related state should be held in ViewModels using StateFlow.

In Composables, this state should be observed using collectAsState().

-Common Patterns:

Info Cards: To display information in various sections, use the InfoCard composable. It takes a title and has a definable content body.

Row Display: To display a label and its value in a single row, use the InfoRow composable.

Permission Handling: The accompanist-permissions library is used for requesting permissions. The permission status should be checked, and the appropriate UI (permission request or information display) should be shown to the user.

##Key Files
Here is a list of important project files and their roles:

-MainActivity.kt: The main entry point of the application. It handles initial setup like language, theme, and launching the DeviceInspectorApp. It also manages the file-saving launcher.

-di/AppModule.kt: The Hilt module that defines how to provide all project dependencies, especially DataSources and the Repository.

-data/repository/DeviceInfoRepository.kt: Acts as the Single Source of Truth. This class manages all calls to the various DataSources and provides the data needed by the ViewModels.

-data/source/ (Package): Contains classes that directly interact with Android APIs to fetch hardware and software information.

-SocDataSource.kt: CPU and GPU information.

-PowerDataSource.kt: Battery and component temperature information.

-MemoryDataSource.kt: RAM and internal storage information.

-SystemDataSource.kt: Operating system, display, and sensor information.

-NetworkDataSource.kt & NetworkToolsDataSource.kt: Network information and related tools.

-ui/viewmodel/ (Package): Contains all the ViewModels for the application.

-DeviceInfoViewModel.kt: The main ViewModel for holding and managing general device data and live data.

-NavigationViewModel.kt: Manages the current navigation state of the app and controls the back stack.

-DashboardViewModel.kt: Manages the order and visibility of dashboard items.

-SettingsViewModel.kt: Manages application settings like theme and language.

-ui/screen/ (Package): Contains all full-screen composables, each constituting a screen in the application.

-DashboardScreen.kt: The main screen that displays the different categories.

-DetailScreen.kt: The screen for displaying the details of a specific category.

-SensorDetailScreen.kt: A dedicated screen for displaying live data from a sensor.

##Other Project Files
Below is a list of other files and their roles in the project:

-build.gradle.kts (Project & App): Gradle files for defining plugins, dependencies, and project build settings.

-gradle/wrapper/gradle-wrapper.properties: Configuration for the Gradle Wrapper, used for build consistency across different environments.

-gradle.properties: Global Gradle settings, such as JVM memory settings.

-gradle/libs.versions.toml: The Version Catalog file for centralized management of library and plugin versions.

-settings.gradle.kts: Settings for project modules and plugin repositories.

-AndroidManifest.xml: The main application manifest, containing definitions for Activities, permissions, and other essential application features.

-KavoshApp.kt: The Application class, annotated with @HiltAndroidApp to set up Hilt at the application level.

-data/model/ (Package): Contains data classes that model the structure of information for different components (e.g., CpuInfo, BatteryInfo, etc.).

-domain/sensor/ (Package): Contains the logic for sensor management, including SensorHandler for registering and receiving sensor data, and SensorState for modeling different sensor states.

-ui/theme/ (Package): Contains files related to the application's theme in Jetpack Compose.

-Theme.kt: Main theme definition, including color palettes for light, dark, and AMOLED modes.

-Color.kt: Definition of base colors.

-Type.kt: Definition of typography styles.

-ui/composables/ (Package): Contains small, reusable composables used across different screens.

-res/drawable/: Contains drawable resources (Vector Drawables) for icons.

-res/mipmap-anydpi/: Contains adaptive icons for the application launcher.

-util/ (Package): Contains utility functions for common tasks like size formatting, text sharing, and checking service statuses.





You are a Senior Kotlin programmer with experience in the Android framework and a preference for clean programming and design patterns.

Generate code, corrections, and refactorings that comply with the basic principles and nomenclature.

### Basic Principles
- Always declare the type of each variable and function (parameters and return value).
  - Avoid using any.
  - Create necessary types.
- Don't leave blank lines within a function.

### Nomenclature

- Use PascalCase for classes.
- Use camelCase for variables, functions, and methods.
- Use underscores_case for file and directory names.
- Use UPPERCASE for environment variables.
  - Avoid magic numbers and define constants.
- Start each function with a verb.
- Use verbs for boolean variables. Example: isLoading, hasError, canDelete, etc.
- Use complete words instead of abbreviations and correct spelling.
  - Except for standard abbreviations like API, URL, etc.
  - Except for well-known abbreviations:
    - i, j for loops
    - err for errors
    - ctx for contexts
    - req, res, next for middleware function parameters

### Functions

- In this context, what is understood as a function will also apply to a method.
- Write short functions with a single purpose. Less than 20 instructions.
- Name functions with a verb and something else.
  - If it returns a boolean, use isX or hasX, canX, etc.
  - If it doesn't return anything, use executeX or saveX, etc.
- Avoid nesting blocks by:
  - Early checks and returns.
  - Extraction to utility functions.
- Use higher-order functions (map, filter, reduce, etc.) to avoid function nesting.
  - Use arrow functions for simple functions (less than 3 instructions).
  - Use named functions for non-simple functions.
- Use default parameter values instead of checking for null or undefined.
- Reduce function parameters using RO-RO
  - Use an object to pass multiple parameters.
  - Use an object to return results.
  - Declare necessary types for input arguments and output.
- Use a single level of abstraction.

### Data

- Use data classes for data.
- Don't abuse primitive types and encapsulate data in composite types.
- Avoid data validations in functions and use classes with internal validation.
- Prefer immutability for data.
  - Use readonly for data that doesn't change.
  - Use as val for literals that don't change.

### Classes

- Follow SOLID principles.
- Prefer composition over inheritance.
- Declare interfaces to define contracts.
- Write small classes with a single purpose.
  - Less than 200 instructions.
  - Less than 10 public methods.
  - Less than 10 properties.

### Exceptions

- Use exceptions to handle errors you don't expect.
- If you catch an exception, it should be to:
  - Fix an expected problem.
  - Add context.
  - Otherwise, use a global handler.

### Testing

- Follow the Arrange-Act-Assert convention for tests.
- Name test variables clearly.
  - Follow the convention: inputX, mockX, actualX, expectedX, etc.
- Write unit tests for each public function.
  - Use test doubles to simulate dependencies.
    - Except for third-party dependencies that are not expensive to execute.
- Write acceptance tests for each module.
  - Follow the Given-When-Then convention.

## Specific to Android

### Basic Principles

- Use clean architecture
  - see repositories if you need to organize code into repositories
- Use repository pattern for data persistence
  - see cache if you need to cache data
- Use MVI pattern to manage state and events in viewmodels and trigger and render them in activities / fragments
  - see keepAlive if you need to keep the state alive
- Use Auth Activity to manage authentication flow
  - Splash Screen
  - Login
  - Register
  - Forgot Password
  - Verify Email
- Use Navigation Component to manage navigation between activities/fragments
- Use MainActivity to manage the main navigation
  - Use BottomNavigationView to manage the bottom navigation
  - Home
  - Profile
  - Settings
  - Patients
  - Appointments
- Use ViewBinding to manage views
- Use Flow / LiveData to manage UI state
- Use xml and fragments instead of jetpack compose
- Use Material 3 for the UI
- Use ConstraintLayout for layouts
### Testing

- Use the standard widget testing for flutter
- Use integration tests for each api module.   
