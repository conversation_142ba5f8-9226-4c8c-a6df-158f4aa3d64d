kotlin version: 2.2.0
error message: java.lang.IllegalStateException: Cannot evaluate IR expression in annotation:
 ERROR_CALL 'Unresolved reference: <Unresolved name: R>#' type=IrErrorType([Error type: Unresolved type for R])

	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstAnnotationTransformer.transformSingleArg(IrConstAnnotationTransformer.kt:89)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstAnnotationTransformer.transformAnnotationArgument(IrConstAnnotationTransformer.kt:61)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstAnnotationTransformer.transformAnnotation(IrConstAnnotationTransformer.kt:52)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstAnnotationTransformer.transformAnnotations(IrConstAnnotationTransformer.kt:43)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstDeclarationAnnotationTransformer$visitAnnotations$1.visitDeclaration(IrConstDeclarationAnnotationTransformer.kt:30)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorVoid.visitFunction(IrVisitorVoid.kt:72)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorVoid.visitSimpleFunction(IrVisitorVoid.kt:144)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorVoid.visitSimpleFunction(IrVisitorVoid.kt:140)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorVoid.visitSimpleFunction(IrVisitorVoid.kt:18)
	at org.jetbrains.kotlin.ir.declarations.IrSimpleFunction.accept(IrSimpleFunction.kt:39)
	at org.jetbrains.kotlin.ir.declarations.IrPackageFragment.acceptChildren(IrPackageFragment.kt:27)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorsKt.acceptChildrenVoid(IrVisitors.kt:23)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstDeclarationAnnotationTransformer$visitAnnotations$1.visitElement(IrConstDeclarationAnnotationTransformer.kt:20)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorVoid.visitPackageFragment(IrVisitorVoid.kt:168)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorVoid.visitFile(IrVisitorVoid.kt:184)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstDeclarationAnnotationTransformer$visitAnnotations$1.visitFile(IrConstDeclarationAnnotationTransformer.kt:25)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorVoid.visitFile(IrVisitorVoid.kt:180)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorVoid.visitFile(IrVisitorVoid.kt:18)
	at org.jetbrains.kotlin.ir.declarations.IrFile.accept(IrFile.kt:27)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorsKt.acceptVoid(IrVisitors.kt:11)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstDeclarationAnnotationTransformer.visitAnnotations(IrConstDeclarationAnnotationTransformer.kt:18)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstTransformerKt.transformConst(IrConstTransformer.kt:55)
	at org.jetbrains.kotlin.ir.interpreter.transformer.IrConstTransformerKt.transformConst$default(IrConstTransformer.kt:26)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter$Companion.evaluateConstants(Fir2IrConverter.kt:625)
	at org.jetbrains.kotlin.fir.pipeline.Fir2IrPipeline.evaluateConstants(convertToIr.kt:323)
	at org.jetbrains.kotlin.fir.pipeline.Fir2IrPipeline.runActualizationPipeline(convertToIr.kt:236)
	at org.jetbrains.kotlin.fir.pipeline.Fir2IrPipeline.convertToIrAndActualize(convertToIr.kt:128)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize(convertToIr.kt:97)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize$default(convertToIr.kt:72)
	at org.jetbrains.kotlin.cli.jvm.compiler.legacy.pipeline.JvmCompilerPipelineKt.convertToIrAndActualizeForJvm(jvmCompilerPipeline.kt:109)
	at org.jetbrains.kotlin.cli.jvm.compiler.legacy.pipeline.JvmCompilerPipelineKt.convertAnalyzedFirToIr(jvmCompilerPipeline.kt:85)
	at org.jetbrains.kotlin.kapt.FirKaptAnalysisHandlerExtension.contextForStubGeneration(FirKaptAnalysisHandlerExtension.kt:221)
	at org.jetbrains.kotlin.kapt.FirKaptAnalysisHandlerExtension.doAnalysis(FirKaptAnalysisHandlerExtension.kt:117)
	at org.jetbrains.kotlin.fir.extensions.FirAnalysisHandlerExtension$Companion.analyze(FirAnalysisHandlerExtension.kt:29)
	at org.jetbrains.kotlin.cli.pipeline.jvm.JvmFrontendPipelinePhase.executePhase(JvmFrontendPipelinePhase.kt:84)
	at org.jetbrains.kotlin.cli.pipeline.jvm.JvmFrontendPipelinePhase.executePhase(JvmFrontendPipelinePhase.kt:56)
	at org.jetbrains.kotlin.cli.pipeline.PipelinePhase.phaseBody(PipelinePhase.kt:68)
	at org.jetbrains.kotlin.cli.pipeline.PipelinePhase.phaseBody(PipelinePhase.kt:58)
	at org.jetbrains.kotlin.config.phaser.NamedCompilerPhase.invoke(CompilerPhase.kt:102)
	at org.jetbrains.kotlin.backend.common.phaser.CompositePhase.invoke(PhaseBuilders.kt:22)
	at org.jetbrains.kotlin.config.phaser.CompilerPhaseKt.invokeToplevel(CompilerPhase.kt:53)
	at org.jetbrains.kotlin.cli.pipeline.AbstractCliPipeline.runPhasedPipeline(AbstractCliPipeline.kt:109)
	at org.jetbrains.kotlin.cli.pipeline.AbstractCliPipeline.execute(AbstractCliPipeline.kt:68)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecutePhased(K2JVMCompiler.kt:78)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecutePhased(K2JVMCompiler.kt:44)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:90)
	at org.jetbrains.kotlin.cli.common.CLICompiler.exec(CLICompiler.kt:352)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunnerBase.runCompiler(IncrementalJvmCompilerRunnerBase.kt:175)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunnerBase.runCompiler(IncrementalJvmCompilerRunnerBase.kt:38)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:504)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:421)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileNonIncrementally(IncrementalCompilerRunner.kt:306)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:133)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:679)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:93)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1806)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base/java.lang.reflect.Method.invoke(Unknown Source)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport$1.run(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport$1.run(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)


